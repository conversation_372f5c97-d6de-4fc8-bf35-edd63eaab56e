from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from handler.webhook_handler_component.helper import ask_user_login
from models_mongo import User
from service.database_mongo_service import DATABASE_SERVICE_MONGO
from service.line_service import send_flex_message
from service.ptp_controller_service import get_farmer_cutter_info, get_farm_info
from utils.config import ENV
from utils.logger import logger
from utils.memory_cache import MemoryCache
from cores.auth_middleware import verify_token

# DATABASE = DATABASE_SERVICE_SQLITE()
DATABASE = DATABASE_SERVICE_MONGO()

router = APIRouter()

class TokenUpdateRequest(BaseModel):
    user_id: str

@router.patch("/user_id/{user_line_id}")
async def update_user_id(
    user_line_id: str,
    request: TokenUpdateRequest,
    token: str = Depends(verify_token)
):
    logger.info(f"Update user ID request: {request}")
    user_record = DATABASE.get_line_user({"line_user_id": user_line_id})
    if user_record:
        if user_record.user_id != request.user_id:
            DATABASE.update_line_user(
                query={"line_user_id": user_line_id},
                updated_field={"user_id": request.user_id, "language": 'english' if ENV == 'dev' else 'thailand'}
            )
    else:
        new_user = User(line_user_id=user_line_id, user_id=request.user_id)
        if ENV == 'dev':
            new_user.language = 'english'
        else:
            new_user.language = 'thailand'
        DATABASE.insert_line_user(new_user)

    try:
        user = get_farmer_cutter_info(request.user_id).get('data', {})
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        raise HTTPException(status_code=404, detail="User not found")

    match user.get('profile', {}).get('role'):
        case "farmer":
            farm_id = user.get('profile', {}).get('supplier_id', {}).get('id')
            if not farm_id:
                raise HTTPException(status_code=404, detail="Cannot get farm ID. Farm of the farmer not found")
            farm = get_farm_info(farm_id, request.user_id)
            farm_name = user.get('profile', {}).get('supplier_id', {}).get('name')
            farmer_name = user.get('first_name', "") + ' ' + user.get('last_name', "")
            farm_address = user.get('profile', {}).get('supplier_id', {}).get('address')
            farm_area = farm.get('area', '_')
            farm_area_unit = farm.get('area_unit', '_')
            flex_message_login_success = {
                "type": "bubble",
                "body": {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": f"""Hello, Durian farmers!\n\n{farm_name}\n🌾 Plant type: Durian ({farm_area} {farm_area_unit})\n🧑‍🌾 Farmer name: {farmer_name}\n📍 Plot location: {farm_address}\n\nYou can now use the Department of Agriculture's durian traceability system to help ensure transparency and international standards in tracking and inspecting your durian. If you need more information or advice, please feel free to ask!"""
                            if ENV == 'dev' else f"""สวัสดีครับ เกษตรกรชาวสวนทุเรียน!\n\n{farm_name}\n🌾 ชนิดพืช: ทุเรียน ({farm_area} ไร่)\n🧑‍🌾 ชื่อเกษตรกร: {farmer_name}\n📍 ที่ตั้งแปลง: {farm_address}\n\nท่านสามารถใช้บริการระบบตรวจสอบ ย้อนกลับ ทุเรียนของกรมวิชาการเกษตรได้แล้วครับ เพื่อช่วยให้การติดตามและตรวจสอบทุเรียนของ ท่านมีความโปร่งใสและเป็นมาตรฐานสากล หากท่านต้องการข้อมูลหรือคำแนะนำเพิ่ม เติม กรุณาถามมาได้เลยครับ!""",
                            "wrap": True
                        }
                    ]
                }
            }
        case "cutter":
            cutter_name = user.get('first_name', "") + ' ' + user.get('last_name', "")
            license_number = user.get('profile').get('metadata', {}).get('license_number', '')
            if license_number == '':
                flex_message_login_success = {
                    "type": "bubble",
                    "body": {
                        "type": "box",
                        "layout": "vertical",
                        "contents": [
                            {
                                "type": "text",
                                "text": f"""Welcome Durian Selectors!\n\n✂️ Cutting Team: {cutter_name}\n‼️ You have not registered as a selector with the Department of Agriculture.\n\nYou are an important force in making Thai durian meet world-class standards!\n\nThe Department of Agriculture's durian traceability system is ready to help you record harvest data easily, quickly, and accurately."""
                                    if ENV == 'dev' else 
                                        f""""ยินดีต้อนรับ นักคัดนักตัดทุเรียน!\n\n✂️ ทีมตัด: {cutter_name}\n‼️ ท่านยังไม่ได้ลงทะเบียนนักคัดนักตัดกับ กรมวิชาการเกษตร\n\nท่านคือกำลังสำคัญของการทำให้ทุเรียน ไทยได้มาตรฐานระดับโลก!\n\nระบบตรวจสอบย้อนกลับทุเรียน ของกรมวิชาการเกษตรพร้อมแล้วที่จะช่วยท่านบันทึกข้อมูลการเก็บเกี่ยวอย่าง ง่ายดาย รวดเร็ว และถูกต้อง""",
                                "wrap": True
                            }
                        ]
                    }
                }
            else:
                flex_message_login_success = {
                    "type": "bubble",
                    "body": {
                        "type": "box",
                        "layout": "vertical",
                        "contents": [
                            {
                                "type": "text",
                                "text": f"""Welcome, Durian Sorting & Cutting Specialist!\n\n✂️ Cutting Team: {cutter_name}\n🪪 Sorting & Cutting ID: {license_number}\n\nYou are the key force behind making Thai durians world-class!\n\nThe Durian Traceability System of the Department of Agricultural Sciences is ready to help you record harvesting data easily, quickly, and accurately."""
                                    if ENV == 'dev' else f"""ยินดีต้อนรับ นักคัดนักตัดทุเรียน!\n\n✂️ ทีมตัด: {cutter_name}\n🪪 หมายเลขนักคัดนักตัด: {license_number}\n\nท่านคือกำลังสำคัญของการทำให้ทุเรียน ไทยได้มาตรฐานระดับโลก!\n\nระบบตรวจสอบย้อนกลับทุเรียน ของกรมวิชาการเกษตรพร้อมแล้วที่จะช่วยท่านบันทึกข้อมูลการเก็บเกี่ยวอย่าง ง่ายดาย รวดเร็ว และถูกต้อง""",
                                "wrap": True
                            }
                        ]
                    }
                }
        case "packing_house_staff":
            flex_message_login_success = {
                "type": "bubble",
                "body": {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": "📦 Hello, Border Checkpoint Officer!\n\nYou can now use the Agricultural Department's traceability system for durians to ensure transparency and compliance with international standards in tracking and verifying your durian production. If you need more information or advice, feel free to ask!"
                                if ENV == 'dev' else "📦 สวัสดี เจ้าหน้าที่โรงคัดบรรจุ! คุณสามารถใช้งานระบบตรวจสอบย้อนกลับทุเรียนของกรมวิชาการเกษตร เพื่อ ตรวจสอบความโปร่งใส และ ความสอดคล้องตามมาตรฐานสากล ในการติดตามและยืนยันกระบวนการผลิตทุเรียนของคุณ หากต้องการข้อมูลเพิ่มเติมหรือคำแนะนำ สามารถสอบถามได้ทุกเมื่อครับ",
                            "wrap": True
                        }
                    ]
                }
            }
        case "border_checkpoint_officer":
            flex_message_login_success = {
                "type": "bubble",
                "body": {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": "🇹🇭 Hello, Border Checkpoint Officer!\n\nYou can now use the Agricultural Department's traceability system for durians to ensure transparency and compliance with international standards in tracking and verifying your durian production. If you need more information or advice, feel free to ask!"
                                if ENV == 'dev' else "🇹🇭 สวัสดี เจ้าหน้าที่ด่านตรวจ! คุณสามารถใช้งานระบบตรวจสอบย้อนกลับทุเรียนของกรมวิชาการเกษตร เพื่อ ตรวจสอบความโปร่งใส และ ความสอดคล้องตามมาตรฐานสากล ในการติดตามและยืนยันข้อมูลการผลิตทุเรียน หากต้องการข้อมูลเพิ่มเติมหรือคำแนะนำ สามารถสอบถามได้ทุกเมื่อครับ",
                            "wrap": True
                        }
                    ]
                }
            }
        case _:
            return {"message": "User ID updated successfully"}
    if flex_message_login_success:
        send_flex_message(user_line_id, flex_message_login_success)
    if user.get('profile', {}).get('role') in ['farmer', 'cutter']:
        flex_message_harvest = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "Do you want to add durian harvesting information?" if ENV == 'dev' else "คุณต้องการเพิ่มข้อมูลการเกี่ยวทุเรียนใช่ หรือไม่?",
                        "wrap": True
                    },
                    {
                        "type": "separator",
                        "margin": "xl"
                    },
                    {
                        "type": "box",
                        "layout": "vertical",
                        "contents": [
                            {
                                "type": "text",
                                "text": "Add durian harvesting information" if ENV == 'dev' else "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน",
                                "align": "center",
                                "color": "#44679b",
                                "size": "sm"
                            }
                        ],
                        "paddingAll": "lg",
                        "action": {
                            "type": "message",
                            "label": "action",
                            "text": "Add durian harvesting information" if ENV == 'dev' else "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน"
                        }
                    }
                ]
            }
        }
        send_flex_message(user_line_id, flex_message_harvest)
    return {"message": "User ID updated successfully"}

class DirectusWebhookRequest(BaseModel):
    user_id: str
    user_line_id: str | None

@router.post("/directus_webhook/user_profile")
async def directus_webhook_user_profile(
    user_profile: DirectusWebhookRequest,
    token: str = Depends(verify_token)
):
    logger.info(f"Directus webhook user profile: {user_profile}")

    user_id = user_profile.user_id
    if not user_id:
        logger.error("User ID not found in the request")
        return {"message": "User ID not found in the request"}

    line_user_id = user_profile.user_line_id
    if line_user_id and line_user_id != 'null':
        user_record = DATABASE.get_line_user({"line_user_id": line_user_id})
        if user_record:
            if user_record.user_id != user_id:
                DATABASE.update_line_user(
                    query={"line_user_id": line_user_id},
                    updated_field={"user_id": user_id, "language": 'english' if ENV == 'dev' else 'thailand'}
                )
        else:
            new_user = User(line_user_id=line_user_id, user_id=user_id)
            if ENV == 'dev':
                new_user.language = 'english'
            else:
                new_user.language = 'thailand'
            DATABASE.insert_line_user(new_user)
        flex_admin_add_user = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "You have been added as a user in our system. You can now use the Department of Agriculture's durian traceability system to help ensure transparency and international standards in tracking and inspecting your durian. If you need more information or advice, please feel free to ask!"
                            if ENV == 'dev' else 
                                """คุณได้ถูกเพิ่มเป็นผู้ใช้ในระบบของเราแล้ว คุณสามารถใช้บริการระบบตรวจสอบย้อนกลับทุเรียนของกรมวิชาการเกษตรได้แล้วครับ เพื่อช่วยให้การติดตามและตรวจสอบทุเรียนของท่านมีความโปร่งใสและเป็นมาตรฐานสากล หากท่านต้องการข้อมูลหรือคำแนะนำเพิ่มเติม กรุณาถามมาได้เลยครับ!""",
                        "wrap": True
                    }
                ]
            }
        }
        flex_message_harvest = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "Do you want to add durian harvesting information?" if ENV == 'dev' else "คุณต้องการเพิ่มข้อมูลการเกี่ยวทุเรียนใช่ หรือไม่?",
                        "wrap": True
                    },
                    {
                        "type": "separator",
                        "margin": "xl"
                    },
                    {
                        "type": "box",
                        "layout": "vertical",
                        "contents": [
                            {
                                "type": "text",
                                "text": "Add durian harvesting information" if ENV == 'dev' else "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน",
                                "align": "center",
                                "color": "#44679b",
                                "size": "sm"
                            }
                        ],
                        "paddingAll": "lg",
                        "action": {
                            "type": "message",
                            "label": "action",
                            "text": "Add durian harvesting information" if ENV == 'dev' else "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน"
                        }
                    }
                ]
            }
        }
        send_flex_message(line_user_id, flex_admin_add_user)
        send_flex_message(line_user_id, flex_message_harvest)
        return {"message": "User ID updated successfully"}

    user_record = DATABASE.get_line_user({"user_id": user_id})
    if user_record:
        DATABASE.update_line_user(
            query={"line_user_id": user_record.line_user_id},
            updated_field={"user_id": None}
        )
        MemoryCache.delete_all_fields(user_record.line_user_id)
        flex_admin_exclude_user = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": """You have been removed from our system. You can no longer use the Department of Agriculture's durian traceability system."""
                                if ENV == 'dev' else 
                                    """คุณได้ถูกลบออกจากระบบของเราแล้ว คุณไม่สามารถใช้บริการระบบตรวจสอบย้อนกลับทุเรียนของกรมวิชาการเกษตรได้อีกต่อไป""",
                        "wrap": True
                    }
                ]
            }
        }
        send_flex_message(user_record.line_user_id, flex_admin_exclude_user)
        logger.info(f"User ID {user_id} removed from the system")
        return {"message": "User ID deleted successfully"}

    return {"message": "No action taken. User ID not found in the database"}

@router.delete("/user_id/{user_line_id}")
async def delete_user_id(
    user_line_id: str,
    token: str = Depends(verify_token)
):
    DATABASE.update_line_user(query={"line_user_id": user_line_id}, updated_field={"user_id": None})
    MemoryCache.delete_all_fields(user_line_id)
    logout_msg = {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": """You have logged out successfully.\n\nIf you want to use my features, please log in again by clicking the button below."""
                            if ENV == 'dev' else 
                                """คุณได้ออกจากระบบเรียบร้อยแล้ว\n\nหากต้องการใช้ฟีเจอร์ของฉันอีกครั้ง กรุณาเข้าสู่ระบบใหม่ โดยคลิกที่ปุ่มด้านล่าง""",
                    "wrap": True
                }
            ]
        }
    }
    send_flex_message(user_line_id, logout_msg)
    ask_user_login(user_line_id, 'english' if ENV == 'dev' else 'thailand')
    return {"message": "User ID deleted successfully"}
