from fastapi import APIRouter, HTTPException, Depends
from utils.durian_info import DURIAN_INFO
from utils.logger import logger
from cores.auth_middleware import verify_token

router = APIRouter(
    prefix="/sync",
    tags=["sync"]
)

@router.get("/variety")
def sync_variety(token: str = Depends(verify_token)):
    """
    Endpoint to sync durian variety information from the main database.
    This will update the variety information in the DurianInfo singleton.
    """
    try:
        success = DURIAN_INFO._fetch_durian_varieties()
        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to sync durian varieties"
            )
        return {"message": "Successfully synced durian varieties"}
    except Exception as e:
        logger.error(f"Error syncing durian varieties: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to sync durian varieties"
        )
