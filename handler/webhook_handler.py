from linebot.v3 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from linebot.v3.webhooks import Event
from linebot.v3.messaging import (
    TextMessage
)

# from models_mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>, User, DATABASE
from handler.webhook_handler_component.follow_event import handle_follow
from handler.webhook_handler_component.helper import <PERSON>AT<PERSON><PERSON><PERSON>, ask_user_login, get_user_language
from handler.webhook_handler_component.image_event import handle_image_message
from handler.webhook_handler_component.location_event import handle_location_message
from handler.webhook_handler_component.postback_event import handle_postback
from handler.webhook_handler_component.text_event import handle_text_message
from service.line_service import send_line_message
from utils.config import ENV, LINE_CHANNEL_SECRET
from utils.language_mapping import get_displayed_texts
from utils.memory_cache import MemoryCache

handler = WebhookHandler(LINE_CHANNEL_SECRET)

"""
Handles default events.
Checks if the user is logged in and processes the event accordingly.
"""
@handler.default()
def handle_default(event: Event):
    # Do not reply to unfollow events
    if event.type == 'unfollow':
        return

    if event.type == "follow":
        handle_follow(event)
        return

    line_user_id = event.source.user_id
    # user = get_logged_in_user(line_user_id)

    # Check if user message is contact us
    # If yes, show the contact us message for user
    if event.type == "message" and event.message.type == "text" and event.message.text in ["Contact us", "ติดต่อเรา"]:
        current_user_language = get_user_language(line_user_id) or 'thailand'
        contact_us_msg = get_displayed_texts("Contact us", current_user_language)[0]
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=contact_us_msg)}
        )
        return

    if not DATABASE.is_user_signed_in(line_user_id):
    # if False:
        # send_line_message(
        #     line_user_id=line_user_id,
        #     reply_token=event.reply_token,
        #     message={"type": "reply", "message": TextMessage(text="Please login to continue.")}
        # )
        ask_user_login(line_user_id, 'english' if ENV == 'dev' else 'thailand')
        return

    if event.type == "postback":
        handle_postback(event)
        return

    if event.type == "message":
        if event.message.type == "text":
            handle_text_message(event)
            return
        if event.message.type == "image":
            handle_image_message(event)
            return
        if event.message.type == "location":
            handle_location_message(event)
            return
    # if event.type == "audio":
    #     hand_audio_message(event)
    # if event.type == "video":
    #     handle_video_message(event)
    # if event.type == "location":
    #     handle_location_message(event)

    language = MemoryCache.get(line_user_id, 'language') or 'thailand'

    # Check if the user is in the harvest flow
    current_state = MemoryCache.get(line_user_id, 'state')
    if current_state:
        msg_invalid_input = get_displayed_texts("Invalid input", language)[0]
        sent_invalid_input_msg = {
            "type": "reply",
            "message": TextMessage(text=msg_invalid_input)
        }
        last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[sent_invalid_input_msg, last_sent_msg]
        )
        return

    msg = get_displayed_texts("Welcome message", language)[0]
    send_line_message(
        line_user_id=line_user_id,
        reply_token=event.reply_token,
        message={"type": "reply", "message": TextMessage(text=msg)}
    )
