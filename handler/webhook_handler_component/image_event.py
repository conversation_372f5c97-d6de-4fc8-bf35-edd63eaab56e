import traceback
from linebot.v3.webhooks import Message<PERSON><PERSON>
from linebot.v3.messaging import TextMessage

from .helper import ConversationState
from utils.memory_cache import MemoryCache
from service.line_service import get_message_binary_content, send_line_message
from service.upload_file import send_bytes_form_data
from utils.config import ENV
from utils.language_mapping import get_displayed_texts
from utils.logger import logger
from state_machine.harvest_flow_manager import HarvestFlowManager

"""
Handles image messages sent by users.
"""
def handle_image_message(event: MessageEvent):
    line_user_id = event.source.user_id
    # user = get_logged_in_user(line_user_id)

    # # Ask user to login if not logged in
    # if not user.access_token:
    #     # Check if the current image is last in the sequence.
    #     # If we don't check this, the user will be prompted to login multiple times because the user may send multiple images.
    #     if not event.message.image_set or event.message.image_set.index == event.message.image_set.total:
    #         ask_user_login(user._id, line_user_id)
    #     return

    current_state = MemoryCache.get(line_user_id, 'state')
    allowed_states = [ConversationState.AWAITING_DURIAN_PHOTO, ConversationState.AWAITING_CUTTER_PHOTO, ConversationState.AWAITING_VEHICLE_PHOTO, ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE]
    if not current_state:
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=get_displayed_texts("Welcome message", 'thailand')[0])}
        )
        return

    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'
    if current_state not in allowed_states:
        msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
        sent_invalid_input_msg = {
            "type": "reply",
            "message": TextMessage(text=msg_invalid_input)
        }
        last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[sent_invalid_input_msg, last_sent_msg]
        )
        return

    image_id = event.message.id

    logger.info(f"Received image message from user {line_user_id}, image ID: {image_id}")

    if current_state == ConversationState.AWAITING_DURIAN_PHOTO:
        if event.message.image_set and event.message.image_set.total > 3:
            if event.message.image_set.index < event.message.image_set.total:
                return
            msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
            sent_invalid_input_msg = {
                "type": "reply",
                "message": TextMessage(text=msg_invalid_input)
            }
            last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=[sent_invalid_input_msg, last_sent_msg]
            )
            return

    try:
        # Check if we should use the new HarvestFlowManager for harvest states
        harvest_states = [
            ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE,
            ConversationState.AWAITING_DURIAN_PHOTO,
            ConversationState.AWAITING_CUTTER_PHOTO,
            ConversationState.AWAITING_VEHICLE_PHOTO
        ]

        if current_state in harvest_states:
            # Get the binary content of the image
            image_data = get_message_binary_content(image_id)
            logger.info(f"Retrieved image data, size: {len(image_data)} bytes")

            # Use HarvestFlowManager for new state machine approach
            harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
            harvest_manager.process_image_input(image_data, event.reply_token)
            return

        # For non-harvest states, handle with legacy logic if needed
        # Get the binary content of the image
        image_data = get_message_binary_content(image_id)
        logger.info(f"Retrieved image data, size: {len(image_data)} bytes")

        # Send image to the remote storage
        response_data = send_bytes_form_data(image_data)
        remote_image_id = response_data['data']['id']
        logger.info(f"Uploaded image to remote storage, image ID: {remote_image_id}")

        # Handle any remaining non-harvest image states here if needed
        # For now, just log that we received an image in an unhandled state
        logger.warning(f"Received image in unhandled state: {current_state}")

        # Send a generic response
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=get_displayed_texts("Image received", current_user_language)[0])}
        )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error processing image: {str(e)}")
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=get_displayed_texts("Error processing image message", current_user_language)[0])}
        )
