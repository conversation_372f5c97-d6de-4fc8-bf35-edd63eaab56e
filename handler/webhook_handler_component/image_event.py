import traceback
from linebot.v3.webhooks import Message<PERSON>vent
from linebot.v3.messaging import TextMessage

from utils.line_utils import create_harvest_question_response
from .helper import D<PERSON><PERSON><PERSON><PERSON>, ConversationState, process_after_uploading_durian_photo
from utils.memory_cache import MemoryCache
from service.line_service import get_message_binary_content, send_line_message
from service.upload_file import send_bytes_form_data
from utils.config import <PERSON>N<PERSON>, FILE_LINK_PREFIX
from utils.language_mapping import get_displayed_texts
from utils.logger import logger
from state_machine.harvest_flow_manager import HarvestFlowManager

"""
Handles image messages sent by users.
"""
def handle_image_message(event: MessageEvent):
    line_user_id = event.source.user_id
    # user = get_logged_in_user(line_user_id)

    # # Ask user to login if not logged in
    # if not user.access_token:
    #     # Check if the current image is last in the sequence.
    #     # If we don't check this, the user will be prompted to login multiple times because the user may send multiple images.
    #     if not event.message.image_set or event.message.image_set.index == event.message.image_set.total:
    #         ask_user_login(user._id, line_user_id)
    #     return

    current_state = MemoryCache.get(line_user_id, 'state')
    allowed_states = [ConversationState.AWAITING_DURIAN_PHOTO, ConversationState.AWAITING_CUTTER_PHOTO, ConversationState.AWAITING_VEHICLE_PHOTO, ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE]
    if not current_state:
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=get_displayed_texts("Welcome message", 'thailand')[0])}
        )
        return

    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'
    if current_state not in allowed_states:
        msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
        sent_invalid_input_msg = {
            "type": "reply",
            "message": TextMessage(text=msg_invalid_input)
        }
        last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[sent_invalid_input_msg, last_sent_msg]
        )
        return

    image_id = event.message.id
    current_data = MemoryCache.get(line_user_id, 'data') or {}

    logger.info(f"Received image message from user {line_user_id}, image ID: {image_id}")

    if current_state == ConversationState.AWAITING_DURIAN_PHOTO:
        if event.message.image_set and event.message.image_set.total > 3:
            if event.message.image_set.index < event.message.image_set.total:
                return
            msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
            sent_invalid_input_msg = {
                "type": "reply",
                "message": TextMessage(text=msg_invalid_input)
            }
            last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=[sent_invalid_input_msg, last_sent_msg]
            )
            return

    try:
        # Check if we should use the new HarvestFlowManager
        if current_state in [ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE]:
            # Get the binary content of the image
            image_data = get_message_binary_content(image_id)
            logger.info(f"Retrieved image data, size: {len(image_data)} bytes")

            # Use HarvestFlowManager for new state machine approach
            harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
            harvest_manager.process_image_input(image_data, event.reply_token)
            return

        # Get the binary content of the image
        image_data = get_message_binary_content(image_id)

        # Log the image size
        logger.info(f"Retrieved image data, size: {len(image_data)} bytes")

        # Send image to the remote storage
        response_data = send_bytes_form_data(image_data)
        remote_image_id = response_data['data']['id']
        image_link = f"{FILE_LINK_PREFIX}/{response_data['data']['filename_disk']}"
        logger.info(f"Uploaded image to remote storage, image ID: {remote_image_id}")

        match current_state:
            case ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE:
                    # if current_data.get('plot_name', None):
                    #     ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                    #     create_farm_plot(
                    #         user_id=ptp_user_id,
                    #         farm_id=current_data['farm_id'],
                    #         plot_name=current_data['plot_name'],
                    #         gap=current_data['gap'],
                    #         area=current_data['area'],
                    #         plot_certificate_image_id=remote_image_id
                    #     )
                    #     list_plot_names = MemoryCache.get(line_user_id, 'list_plot_names') or []
                    #     list_plot_names.append(current_data['plot_name'])

                    #     list_farm_plots = get_list_farm_plots(current_data['farm_id'], ptp_user_id)
                    #     mapping_plot_name_to_plot_object = {}
                    #     for plot in list_farm_plots:
                    #         mapping_plot_name_to_plot_object[plot['plot_name']] = plot

                    #     # Save mapping plot name to id and list plot names for later use
                    #     MemoryCache.set(line_user_id, 'list_plot_names', list_plot_names)
                    #     MemoryCache.set(line_user_id, 'mapping_plot_name_to_plot_object', mapping_plot_name_to_plot_object)

                    #     del current_data['plot_name']
                    #     del current_data['gap']
                    #     del current_data['area']
                    #     MemoryCache.set(line_user_id, 'data', current_data)
                    #     # Move to plot selection
                    #     MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_SELECTION)
                    #     plot_prompt = create_harvest_question_response(
                    #         current_user_language,
                    #         ConversationState.AWAITING_PLOT_SELECTION,
                    #         extra_context={'plots': list_plot_names}
                    #     )
                    #     send_line_message(
                    #         line_user_id=line_user_id,
                    #         reply_token=event.reply_token,
                    #         message=plot_prompt
                    #     )

                    #     return

                    # else:

                # Save plot certificate image info
                current_data['selected_plots'][-1]['plot_certificate_link'] = image_link
                current_data['selected_plots'][-1]['plot_certificate_image_id'] = remote_image_id
                MemoryCache.set(line_user_id, 'data', current_data)

                list_plot_names = MemoryCache.get(line_user_id, 'list_plot_names')
                user_role = MemoryCache.get(line_user_id, 'user_role')

                if user_role == 'cutter' and len(list_plot_names) == 0:
                    # Move to next step: ask harvest date
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_HARVEST_DATE)
                    harvest_date_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_HARVEST_DATE)
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=harvest_date_prompt
                    )
                    return

                # Move to ask adding additional plots
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADDITIONAL_PLOTS)
                more_plots_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADDITIONAL_PLOTS)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=more_plots_prompt
                )

            case ConversationState.AWAITING_DURIAN_PHOTO:

                # Save image link and id
                current_data['durian_image_ids'] = current_data.get('durian_image_ids', []) + [remote_image_id]
                current_data['image_links'] = current_data.get('image_links', []) + [image_link]

                # Check if the current image is last in the sequence
                if event.message.image_set and event.message.image_set.index < event.message.image_set.total:
                    # Save data before process next image
                    MemoryCache.set(line_user_id, 'data', current_data)
                    return

                # if (not event.message.image_set or event.message.image_set.total < 3) and len(current_data['durian_image_ids']) < 3:
                #     # Save data before move to next step
                #     MemoryCache.set(line_user_id, 'data', current_data)
                #     prompt_ask_more_photos = create_harvest_question_response(
                #         current_user_language,
                #         ConversationState.AWAITING_DURIAN_PHOTO,
                #         extra_context={'allow_skip': True}
                #     )
                #     send_line_message(
                #         line_user_id=line_user_id,
                #         reply_token=event.reply_token,
                #         message=prompt_ask_more_photos
                #     )
                #     return

                process_after_uploading_durian_photo(line_user_id, current_data, current_user_language, event.reply_token)

            case ConversationState.AWAITING_CUTTER_PHOTO:
                # Save cutter image info
                current_data['cutter_image_id'] = remote_image_id
                current_data['cutter_image_link'] = image_link
                MemoryCache.set(line_user_id, 'data', current_data)

                # Check if the current image is last in the sequence
                if event.message.image_set and event.message.image_set.index < event.message.image_set.total:
                    return

                # Move to ask vehicle photo
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_PHOTO)
                vehicle_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_PHOTO)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=vehicle_photo_prompt
                )

                # # Set next state and send response
                # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_NAME)
                # cutter_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_NAME)
                # send_line_message(
                #     line_user_id=line_user_id,
                #     reply_token=event.reply_token,
                #     message=cutter_name_prompt
                # )

            case ConversationState.AWAITING_VEHICLE_PHOTO:
                # Save vehicle image info
                current_data['vehicle_image_id'] = remote_image_id
                current_data['vehicle_image_link'] = image_link

                # TODO: Use Computer Vision service to get vehicle registration details
                # current_data['vehicle_registration_number'] = "77D1-80105"
                # current_data['vehicle_registration_province'] = "BinhDinh"
                # For now just save the image
                MemoryCache.set(line_user_id, 'data', current_data)

                # # Move to send/draft state
                # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_SEND_OR_DRAFT)
                # send_or_draft_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_SEND_OR_DRAFT)
                # send_line_message(
                #     line_user_id=line_user_id,
                #     reply_token=event.reply_token,
                #     message=send_or_draft_prompt
                # )

                # Move to ask vehicle registration number
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_REGISTRATION_NUMBER)
                vehicle_registration_number_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_REGISTRATION_NUMBER)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=vehicle_registration_number_prompt
                )

            case _:
                return

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error processing image: {str(e)}")
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=get_displayed_texts("Error processing image message", current_user_language)[0])}
        )
