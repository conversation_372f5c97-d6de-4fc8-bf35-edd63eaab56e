from copy import copy
from service.line_service import send_flex_message, send_line_message
from service.ptp_controller_service import get_farmer_cutter_info, get_line_jwt_token, get_list_cutters
from utils.config import ENV, FILE_LINK_PREFIX, LINE_BOT_ID, M_WEB_APP_URL
from utils.flex_message import create_cutter_info_flex_message, create_durian_info_flex_message, create_login_button, create_logout_button, create_relogin_button
from utils.logger import logger
from service.database_mongo_service import DATABASE_SERVICE_MONGO
from utils.line_utils import create_harvest_question_response
from utils.memory_cache import MemoryCache
from utils.durian_info import DURIAN_INFO


DATABASE = DATABASE_SERVICE_MONGO()

class ConversationState:
    INIT = "INIT"
    AWAITING_HARVEST_LOT_NAME = "AWAITING_HARVEST_LOT_NAME"
    AWAITING_FARM_SELECTION = "AWAITING_FARM_SELECTION"
    AWAITING_PLOT_SELECTION = "AWAITING_PLOT_SELECTION"
    AWAITING_PLOT_CERTIFICATE_IMAGE = "AWAITING_PLOT_CERTIFICATE_IMAGE"
    AWAITING_ADDITIONAL_PLOTS = "AWAITING_ADDITIONAL_PLOTS"
    AWAITING_VARIETY_SELECTION = "AWAITING_VARIETY_SELECTION"
    AWAITING_ADD_VARIETY_NAME = "AWAITING_ADD_VARIETY_NAME"
    AWAITING_ADDITIONAL_VARIETIES = "AWAITING_ADDITIONAL_VARIETIES"
    AWAITING_BLOOMING_DATE_PER_VARIETY = "AWAITING_BLOOMING_DATE_PER_VARIETY"
    AWAITING_GRADE_WEIGHT_PER_VARIETY = "AWAITING_GRADE_WEIGHT_PER_VARIETY"
    AWAITING_WEIGHT_PER_VARIETY = "AWAITING_WEIGHT_PER_VARIETY"
    AWAITING_ADDITIONAL_GRADES = "AWAITING_ADDITIONAL_GRADES"
    AWAITING_GRADE_WEIGHT_CONFIRMATION = "AWAITING_GRADE_WEIGHT_CONFIRMATION"
    AWAITING_BLOOMING_DATE = "AWAITING_BLOOMING_DATE"
    AWAITING_HARVEST_DATE = "AWAITING_HARVEST_DATE"
    AWAITING_DURIAN_PHOTO = "AWAITING_DURIAN_PHOTO"
    AWAITING_CUTTER_SELECTION = "AWAITING_CUTTER_SELECTION"
    AWAITING_CUTTER_PHOTO = "AWAITING_CUTTER_PHOTO"
    AWAITING_CUTTER_NAME = "AWAITING_CUTTER_NAME"
    AWAITING_CUTTER_REGISTRATION = "AWAITING_CUTTER_REGISTRATION"
    AWAITING_CUTTER_REGISTRATION_NUMBER = "AWAITING_CUTTER_REGISTRATION_NUMBER"
    AWAITING_VEHICLE_PHOTO = "AWAITING_VEHICLE_PHOTO"
    AWAITING_VEHICLE_REGISTRATION_NUMBER = "AWAITING_VEHICLE_REGISTRATION_NUMBER"
    AWAITING_VEHICLE_REGISTRATION_PROVINCE = "AWAITING_VEHICLE_REGISTRATION_PROVINCE"
    AWAITING_SEND_OR_DRAFT = "AWAITING_SEND_OR_DRAFT"
    AWAITING_PACKING_HOUSE = "AWAITING_PACKING_HOUSE"
    AWAITING_FINAL_CONFIRMATION = "AWAITING_FINAL_CONFIRMATION"
    AWAITING_ADD_FARM_NAME = "AWAITING_ADD_FARM_NAME"
    AWAITING_ADD_FARM_LOCATION = "AWAITING_ADD_FARM_LOCATION"
    AWAITING_ADD_PLOT_NAME = "AWAITING_ADD_PLOT_NAME"
    AWAITING_ADD_PLOT_ID = "AWAITING_ADD_PLOT_ID"
    AWAITING_ADD_PLOT_GAP = "AWAITING_ADD_PLOT_GAP"
    AWAITING_ADD_PLOT_AREA = "AWAITING_ADD_PLOT_AREA"
    AWAITING_ADD_PLOT_CONFIRMATION = "AWAITING_ADD_PLOT_CONFIRMATION"


def get_user_language(line_user_id: str):
    cached_lang = MemoryCache.get(line_user_id, 'language')
    if cached_lang:
        return cached_lang
    user_language_in_db = DATABASE.get_user_language(line_user_id)
    if user_language_in_db:
        MemoryCache.set(line_user_id, 'language', user_language_in_db)
    return user_language_in_db

"""
Sends a login button to the user.
"""
def ask_user_login(line_user_id, current_user_language):
    user_line_jwt_token = get_line_jwt_token(line_user_id)
    login_url = f"{M_WEB_APP_URL}/sign-in?channel=line-app&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
    # Log the login URL
    logger.info(f"Login URL: {login_url}")
    login_button = create_login_button(current_user_language, login_url)
    send_flex_message(line_user_id, login_button)

def show_logout_button(line_user_id, current_user_language):
    user_line_jwt_token = get_line_jwt_token(line_user_id)
    logout_url = f"{M_WEB_APP_URL}/main?channel=line-app&page=settings&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
    # Log the logout URL
    logger.info(f"Logout URL: {logout_url}")
    login_button = create_logout_button(current_user_language, logout_url)
    send_flex_message(line_user_id, login_button)

def show_login_button(line_user_id, current_user_language):
    user_line_jwt_token = get_line_jwt_token(line_user_id)
    login_url = f"{M_WEB_APP_URL}/sign-in?channel=line-app&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
    # Log the login URL
    logger.info(f"Login URL: {login_url}")
    login_button = create_login_button(current_user_language, login_url)
    send_flex_message(line_user_id, login_button)

def show_relogin_button(line_user_id, current_user_language):
    user_line_jwt_token = get_line_jwt_token(line_user_id)
    login_url = f"{M_WEB_APP_URL}/sign-in?channel=line-app&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
    # Log the login URL
    logger.info(f"Login URL: {login_url}")
    relogin_button = create_relogin_button(current_user_language, login_url)
    send_flex_message(line_user_id, relogin_button)

def send_durian_info(line_user_id: str, data: dict, language: str = 'thailand'):
    harvest_lot_name = data.get('harvest_lot_name', ' ')
    farm_name = data.get('farm_name', ' ')
    farm_address = data.get('farm_address', ' ')
    batchlot = data.get('batchlot', ' ')
    plots = data.get('selected_plots', [])
    varieties = copy(data.get('selected_varieties', []))
    raw_variety_details: dict = data.get('variety_details', {})
    variety_details = {}
    for index, variety in enumerate(varieties):
        if variety == 'other':
            variety_name = raw_variety_details['other']['variety_name']
        else:
            variety_name = DURIAN_INFO.mapping_variety_value_to_text(variety, language)
        grades_weights = {}
        for grade_value, weight in raw_variety_details[variety]['grades_weights'].items():
            grade_name = DURIAN_INFO.mapping_grade_value_to_text(variety, grade_value, language)
            grades_weights[grade_name] = weight
        variety_details[variety_name] = {
            'grades_weights': grades_weights,
            'blooming_date': raw_variety_details[variety]['blooming_date']
        }
    total_weight = data.get('total_weight', 0.0)
    image_links = data.get('image_links', [])
    harvest_date = data.get('harvest_date', ' ')
    packing_house = data.get('packing_house', ' ')
    cutter_name = data.get('cutter_name', ' ')
    cutter_avatar_link = data.get('cutter_image_link', ' ')
    cutter_registration = data.get('cutter_registration', False)
    doa_number = data.get('cutter_registration_number', None)
    vehicle_image_link = data.get('vehicle_image_link', ' ')
    vehicle_registration_number = data.get('vehicle_registration_number', ' ')
    vehicle_registration_province = data.get('vehicle_registration_province', ' ')
    durian_info_flex_msg = create_durian_info_flex_message(
        language=MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand',
        harvest_lot_name=harvest_lot_name,
        batchlot=batchlot,
        farm_name=farm_name,
        farm_address=farm_address,
        plots=plots,
        variety_details=variety_details,
        total_weight=total_weight,
        image_links=image_links,
        harvest_date=harvest_date,
        packing_house=packing_house,
        cutter_name=cutter_name,
        cutter_avatar_link=cutter_avatar_link,
        cutter_registration=cutter_registration,
        doa_number=doa_number,
        vehicle_image_link=vehicle_image_link,
        vehicle_registration_number=vehicle_registration_number,
        vehicle_registration_province=vehicle_registration_province
    )
    send_flex_message(line_user_id, durian_info_flex_msg)
    # send_flex_message(line_user_id, create_flex_message_confirm_harvest(MemoryCache.get(line_user_id, 'language')))

def send_cutter_info(line_user_id: str, current_data: dict, current_user_language: str):
    cutter_info_flex_msg = create_cutter_info_flex_message(
        language=current_user_language,
        cutter_name=current_data['cutter_name'],
        avatar_link=current_data['cutter_image_link'],
        cutter_registration=current_data.get('cutter_registration', False),
        doa_number=current_data.get('cutter_registration_number', None),
        vehicle_image_link=current_data['vehicle_image_link'],
        vehicle_registration_number=current_data['vehicle_registration_number'],
        vehicle_registration_province=current_data['vehicle_registration_province']
    )
    send_flex_message(line_user_id, cutter_info_flex_msg)

def process_after_uploading_durian_photo(line_user_id, current_data, current_user_language, reply_token):
    try:
        current_data['durian_image_ids'] = current_data.get('durian_image_ids') or []

        # Get user profile and check role
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_info = get_farmer_cutter_info(ptp_user_id)
        user_role = user_info.get('data', {}).get('profile', {}).get('role', '')
        
        if user_role == 'cutter':
            # For cutters, get their info from profile
            current_data['cutter_id'] = user_info['data']['id']
            current_data['cutter_name'] = user_info['data']['first_name'] + ' ' + user_info['data']['last_name']
            current_data['cutter_registration_number'] = user_info['data']['profile'].get('metadata', {}).get('license_number', '')
            current_data['cutter_image_id'] = user_info.get('data', {}).get('avatar', {}).get('id', '')
            current_data['cutter_image_link'] = f"{FILE_LINK_PREFIX}/{user_info.get('data', {}).get('avatar', {}).get('filename_disk', '')}"
            current_data['cutter_profile_id'] = user_info.get('data', {}).get('profile', {}).get('id', '')
            
            # Verify registration
            current_data['cutter_registration'] = user_info.get('data', {}).get('profile', {}).get('metadata', {}).get('is_certified') or False
                
            # Move to vehicle photo
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_PHOTO)
            vehicle_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_PHOTO)
            
            # Send vehicle photo prompt
            send_line_message(
                line_user_id=line_user_id,
                reply_token=reply_token,
                message=vehicle_photo_prompt
            )
            
        else:
            MemoryCache.set(line_user_id, 'data', current_data)
            # For farmers, ask for cutter selection
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_SELECTION)
            cutters = get_list_cutters(ptp_user_id)
            cutter_selection_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_CUTTER_SELECTION,
                extra_context={'cutters': cutters}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=reply_token,
                message=cutter_selection_prompt
            )
            # MemoryCache.set(line_user_id, 'data', current_data)
            # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_PHOTO)
            # cutter_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_PHOTO)
            
            # # Send info and next prompt
            # send_line_message(
            #     line_user_id=line_user_id,
            #     reply_token=reply_token,
            #     message=cutter_photo_prompt
            # )
        
    except Exception as e:
        logger.error(f"Error processing data: {str(e)}")
        if hasattr(e, 'response'):
            logger.error(f"API Response: {e.response.text}")
        raise