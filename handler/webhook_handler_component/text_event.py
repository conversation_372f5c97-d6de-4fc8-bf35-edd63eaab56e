from datetime import timed<PERSON><PERSON>
import re
import traceback
from linebot.v3.webhooks import Message<PERSON><PERSON>
from linebot.v3.messaging import TextMessage

from service.ptp_controller_service import check_existing_plot, create_farm_plot, get_farmer_cutter_info, get_line_jwt_token, get_list_farm_plots, get_list_farms, get_list_packing_houses, get_product_info, send_durian_event_harvest, update_durian_event_harvest
from utils.config import ENV, LINE_BOT_ID, M_WEB_APP_URL
from utils.exception import CharacterLimitExceededError, DataExistsError, SpecialCharacterError
from utils.string_util import format_plot_gap, format_plot_id
from utils.time_util import convert_to_datetime
from .helper import DATABASE, ConversationState, get_user_language, process_after_uploading_durian_photo, send_cutter_info, send_durian_info, show_login_button, show_logout_button, show_relogin_button
from utils.memory_cache import MemoryCache
from service.line_service import send_line_message
from utils.language_mapping import get_displayed_texts
from utils.line_utils import create_line_button_message, create_selection_buttons, create_harvest_question_response
from utils.logger import logger
from utils.durian_info import DURIAN_INFO

"""
Handles text messages sent by users.
Checks if the user is logged in and processes the message accordingly.
"""
def handle_text_message(event: MessageEvent):
    line_user_id = event.source.user_id
    text = event.message.text

    # Get user preferred language
    if ENV == 'dev':
        if not get_user_language(line_user_id) and text.lower() not in ["english", "thailand"]:
            send_line_message(
                line_user_id=line_user_id,
                message={"type": "flex", "message": create_selection_buttons(title="Please select your preferred language:", items=['English', 'Thailand'])}
            )
            MemoryCache.set(line_user_id, 'language', None)
            return

    # Set user preferred language
    if text.lower() in ["english", "thailand"]:
        MemoryCache.set(line_user_id, 'language', text.lower())
        DATABASE.save_user_language(line_user_id, text.lower())
        send_line_message(
            reply_token=event.reply_token,
            message={
                "type": "reply",
                "message": [
                    TextMessage(text=f"Language set to {text}."),
                    TextMessage(text=get_displayed_texts("Welcome message", MemoryCache.get(line_user_id, 'language'))[0])
                ]
            }
        )
        return

    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'

    if text in ["Record Durian", "Harvest", "Add durian harvesting information", "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน", "เก็บเกี่ยว", "จะตัดทุเรียนแล้ว", "วันนี้มีตัด", "I want to log durians today", "Record harvest", "บันทึกทุเรียน", "เพิ่มการเก็บเกี่ยวใหม่", "Add new record"]:
        if not DURIAN_INFO.is_varieties_available():
            msg = get_displayed_texts("Out of service", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=msg)}
            )
            return
        # Check user role
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_info = get_farmer_cutter_info(ptp_user_id)
        user_role = user_info['data']['profile']['role']
        
        if user_role not in ['farmer', 'cutter']:
            permission_msg = get_displayed_texts("Permission denied", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=permission_msg)}
            )
            return

        MemoryCache.delete_all_except(line_user_id, ['language', 'signed_in'])
        MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_HARVEST_LOT_NAME)
        MemoryCache.set(line_user_id, 'data', {})
        MemoryCache.set(line_user_id, 'user_role', user_role)
        instruction_msg = create_harvest_question_response(
            current_user_language,
            ConversationState.INIT,
            extra_context={'user_role': user_role}
        )
        lot_name_msg = create_harvest_question_response(current_user_language, ConversationState.AWAITING_HARVEST_LOT_NAME)
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[instruction_msg, lot_name_msg]
        )
        return
        
    if text in ["View harvest history", "ดูประวัติการเก็บเกี่ยวทุเรียน", "ประวัติศาสตร์", "View durian harvest history", "ประวัติการเก็บเกี่ยวทุเรียน"]:
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_info = get_farmer_cutter_info(ptp_user_id)
        user_role = user_info['data']['profile']['role']
        if user_role not in ['farmer', 'cutter']:
            deny_view_history_msg = get_displayed_texts("Deny view history", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=deny_view_history_msg)}
            )
            return

        view_history_link = f"{M_WEB_APP_URL}/main?channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={get_line_jwt_token(line_user_id)}"
        view_history_msg = get_displayed_texts("View harvest history", current_user_language)[0]
        view_history_prompt = {
            "type": "flex",
            "message": create_line_button_message(
                displayed_message=view_history_msg,
                btn_title="View harvest history" if current_user_language == 'english' else "ดูประวัติการเก็บเกี่ยว",
                uri=view_history_link
            )
        }
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=view_history_prompt
        )
        return

    if text.lower() in ["logout", "sign out", "log out", "ออกจากระบบ"]:
        show_logout_button(line_user_id, current_user_language)
        return

    if text.lower() in ["relogin", "re-login", "เข้าสู่ระบบใหม่อีกครั้ง", "login", "เข้าสู่ระบบ"]:
        show_relogin_button(line_user_id, current_user_language)
        return

    current_state = MemoryCache.get(line_user_id, 'state')
    current_data = MemoryCache.get(line_user_id, 'data') or {}

    if not current_state:
        msg = get_displayed_texts("Welcome message", current_user_language)[0]
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=msg)}
        )
        return

    ## Process event harvesting
    try:
        if current_state == ConversationState.AWAITING_HARVEST_LOT_NAME:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Harvest lot name should not be blank")

            # Check if the text is not longer than 100 characters
            if len(text.strip()) > 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            current_data['harvest_lot_name'] = text.strip()

            # get user role from cache
            user_role = MemoryCache.get(line_user_id, 'user_role')
            ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
            if user_role == 'farmer':
                # Get list of plots
                farmer_info = get_farmer_cutter_info(ptp_user_id)
                supplier_id = farmer_info.get('data', {}).get('profile', {}).get('supplier_id', {})
                farm_id = supplier_id.get('id')
                if not farm_id:
                    raise Exception("Farm ID not found. User ID: %s" % ptp_user_id)
                list_farm_plots = get_list_farm_plots(farm_id, ptp_user_id)
                list_plot_names = []
                mapping_plot_name_to_plot_object = {}
                for plot in list_farm_plots:
                    list_plot_names.append(plot['name'])
                    mapping_plot_name_to_plot_object[plot['name']] = plot

                # Save mapping plot name to id and list plot names for later use
                MemoryCache.set(line_user_id, 'list_plot_names', list_plot_names)
                MemoryCache.set(line_user_id, 'mapping_plot_name_to_plot_object', mapping_plot_name_to_plot_object)

                current_data['farm_id'] = farm_id
                current_data['farm_name'] = supplier_id.get('name')
                current_data['farm_address'] = supplier_id.get('address')
                current_data['position_longitude'] = supplier_id.get('position_longitude')
                current_data['position_latitude'] = supplier_id.get('position_latitude')

                MemoryCache.set(line_user_id, 'data', current_data)
                # Move to plot selection
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_SELECTION)
                plot_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PLOT_SELECTION,
                    extra_context={
                        'plots': list_plot_names,
                        'allow_add_new_plot': True
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=plot_prompt
                )
            elif user_role == 'cutter':
                MemoryCache.set(line_user_id, 'data', current_data)
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_FARM_SELECTION)
                # Get list of farms
                list_farms = get_list_farms(ptp_user_id)
                displayed_farms = []
                mapping_farm_name_to_farn_object = {}
                for farm in list_farms:
                    # TODO: process the case that 2 farms have the same name, we need to add farmer name to the farm name
                    farm_name = farm['name']
                    if farm_name in displayed_farms:
                        farm_name = f"{farm_name} - {farm['farmer_name']}"
                    displayed_farms.append(farm_name)
                    mapping_farm_name_to_farn_object[farm_name] = farm
                # Save mapping farm name to id and displayed farms for later use
                MemoryCache.set(line_user_id, 'mapping_farm_name_to_farn_object', mapping_farm_name_to_farn_object)
                MemoryCache.set(line_user_id, 'displayed_farms', displayed_farms)

                farm_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_FARM_SELECTION,
                    extra_context={'farms': displayed_farms}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=farm_prompt
                )

        elif current_state == ConversationState.AWAITING_FARM_SELECTION:
            mapping_farm_name_to_farn_object = MemoryCache.get(line_user_id, 'mapping_farm_name_to_farn_object')
            if text in mapping_farm_name_to_farn_object:
                # Save farm name and id
                current_data['farm_name'] = text
                current_data['farm_id'] = mapping_farm_name_to_farn_object[text]['id']
                current_data['farm_address'] = mapping_farm_name_to_farn_object[text]['address']
                current_data['position_longitude'] = mapping_farm_name_to_farn_object[text]['position_longitude']
                current_data['position_latitude'] = mapping_farm_name_to_farn_object[text]['position_latitude']
                MemoryCache.set(line_user_id, 'data', current_data)
                # Get list of plot names
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                list_farm_plots = get_list_farm_plots(current_data['farm_id'], ptp_user_id)

                # Check if the farm have plots
                # If not, do not allow the cutter to harvest that farm
                if len(list_farm_plots) == 0:
                    empty_plot_prompt = create_harvest_question_response(current_user_language, "PLOT_NOT_EXIST")
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=empty_plot_prompt
                    )
                    # Delete cache data for current user
                    MemoryCache.delete_all_except(line_user_id, ['language'])
                    return

                list_plot_names = []
                mapping_plot_name_to_plot_object = {}
                for plot in list_farm_plots:
                    list_plot_names.append(plot['name'])
                    mapping_plot_name_to_plot_object[plot['name']] = plot

                # Save mapping plot name to id and list plot names for later use
                MemoryCache.set(line_user_id, 'list_plot_names', list_plot_names)
                MemoryCache.set(line_user_id, 'mapping_plot_name_to_plot_object', mapping_plot_name_to_plot_object)

                # Move to plot selection
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_SELECTION)
                plot_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PLOT_SELECTION,
                    extra_context={'plots': list_plot_names}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=plot_prompt
                )
            elif text in ["+ เพิ่มสวน", "+ Add more farms"]:
                # Add a new farm
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_FARM_NAME)
                add_farm_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_FARM_NAME)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=add_farm_name_prompt
                )
            else:
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_ADD_FARM_NAME:
            current_data['farm_name'] = text
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_FARM_LOCATION)
            add_farm_location_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_FARM_LOCATION)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=add_farm_location_prompt
            )

        elif current_state == ConversationState.AWAITING_ADD_FARM_LOCATION:
            # Invalid input because location should be in location event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid input for location")

        elif current_state == ConversationState.AWAITING_PLOT_SELECTION:
            list_plot_names: list = MemoryCache.get(line_user_id, 'list_plot_names') or []
            mapping_plot_name_to_plot_object = MemoryCache.get(line_user_id, 'mapping_plot_name_to_plot_object') or {}
            if text in list_plot_names:
                # Save selected plot
                current_data['selected_plots'] = current_data.get('selected_plots', []) + [mapping_plot_name_to_plot_object[text]]
                # Remove selected plot from list_plot_names
                list_plot_names.remove(text)

                # Save data to cache
                MemoryCache.set(line_user_id, 'list_plot_names', list_plot_names)
                MemoryCache.set(line_user_id, 'data', current_data)

                # Move to next stage
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE)
                plot_certificate_image_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE,
                    extra_context={'plot_name': text}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=plot_certificate_image_prompt
                )

            elif text in ["+ เพิ่มแปลง", "+ Add more plot"]:
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_NAME)
                add_plot_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_PLOT_NAME)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=add_plot_name_prompt
                )

            # Invalid input - stay in current state and re-send prompt
            else:
                raise ValueError("%s: Invalid select option" % current_state)

        elif current_state == ConversationState.AWAITING_ADD_PLOT_NAME:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Plot name should not be blank")
            if len(text.strip()) >= 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)
            ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
            existing_plot = check_existing_plot(current_data['farm_id'], text.strip(), ptp_user_id)
            if existing_plot.get('exists', False):
                raise DataExistsError(value=text.strip(), step=current_state)

            current_data['plot_name'] = text.strip()
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_GAP)
            add_plot_gap_confirmation_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_ADD_PLOT_GAP,
                extra_context={'plot_name': text.strip()}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=add_plot_gap_confirmation_prompt
            )

        elif current_state == ConversationState.AWAITING_ADD_PLOT_GAP:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: GAP should not be blank")
            if len(text.strip()) >= 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            if text in ["Skip", "ข้าม"]:
                current_data['gap'] = None
            else:
                # Check if the gap contains 17 numbers
                digits = re.findall(r'\d', text.strip())
                if len(digits) != 17:
                    msg_invalid_number_of_digits_in_gap = get_displayed_texts("Invalid number of digits in GAP", current_user_language)[0]
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message={"type": "reply", "message": TextMessage(text=msg_invalid_number_of_digits_in_gap)}
                    )
                    return

                current_data['gap_digits'] = ''.join(digits)
                current_data['gap'] = format_plot_gap(current_user_language, current_data['gap_digits'])
                

            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_AREA)
            add_plot_area_confirmation_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_ADD_PLOT_AREA,
                extra_context={'plot_name': current_data['plot_name']}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=add_plot_area_confirmation_prompt
            )

        elif current_state == ConversationState.AWAITING_ADD_PLOT_AREA:
            try:
                area = float(text)
                if area <= 0:
                    raise ValueError(f"{current_state}: Invalid area. Please enter a number greater than 0.")
            except ValueError:
                area_prompt = TextMessage(
                    text=get_displayed_texts("Invalid area", current_user_language)[0]
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message={"type": "reply", "message": area_prompt}
                )
                return
            current_data['area'] = area
            MemoryCache.set(line_user_id, 'data', current_data)
            # Move to add plot id
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_ID)
            add_plot_id_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_ADD_PLOT_ID,
                extra_context={'plot_name': current_data['plot_name']}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=add_plot_id_prompt
            )

        elif current_state == ConversationState.AWAITING_ADD_PLOT_ID:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Plot ID should not be blank")
            # if len(text.strip()) >= 100:
            #     raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            if text in ["Skip", "ข้าม"]:
                current_data['plot_id'] = None
            else:
                # Check if plot id contains 20 numbers
                digits = re.findall(r'\d', text.strip())
                if len(digits) != 20:
                    msg_invalid_number_of_digits_in_plot_id = get_displayed_texts("Invalid number of digits in plot ID", current_user_language)[0]
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message={"type": "reply", "message": TextMessage(text=msg_invalid_number_of_digits_in_plot_id)}
                    )
                    return
                current_data['plot_id'] = format_plot_id(''.join(digits))
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_CONFIRMATION)
            add_plot_name_confirmation_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_ADD_PLOT_CONFIRMATION,
                extra_context={
                    'plot_name': current_data['plot_name'],
                    'gap': current_data['gap'],
                    'area': current_data['area'],
                    'plot_id': current_data['plot_id']
                }
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=add_plot_name_confirmation_prompt
            )

        elif current_state == ConversationState.AWAITING_ADD_PLOT_CONFIRMATION:
            if text in ["Confirm", "ถูกต้อง"]:
                # Create plot
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                create_farm_plot(
                    user_id=ptp_user_id,
                    farm_id=current_data.get('farm_id'),
                    plot_name=current_data.get('plot_name'),
                    gap=current_data.get('gap_digits'),
                    area=current_data.get('area'),
                    plot_id=current_data.get('plot_id')
                )
                list_plot_names = MemoryCache.get(line_user_id, 'list_plot_names') or []

                # Get list of plots
                list_farm_plots = get_list_farm_plots(current_data['farm_id'], ptp_user_id)
                mapping_plot_name_to_plot_object = {}
                # mapping plot name to plot object
                for plot in list_farm_plots:
                    mapping_plot_name_to_plot_object[plot['name']] = plot

                # Save mapping plot name to id and for later use
                MemoryCache.set(line_user_id, 'mapping_plot_name_to_plot_object', mapping_plot_name_to_plot_object)

                # Check if there are more plots to select
                # If not, go directly to ask plot certificate image
                if len(list_plot_names) == 0:
                    # Save selected plot
                    current_data['selected_plots'] = current_data.get('selected_plots', []) + [mapping_plot_name_to_plot_object[current_data['plot_name']]]
                    # Move to ask plot certificate image
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE)
                    plot_certificate_image_prompt = create_harvest_question_response(
                        current_user_language,
                        ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE,
                        extra_context={'plot_name': current_data['plot_name']}
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=plot_certificate_image_prompt
                    )
                    # Remove temporary data
                    if 'plot_name' in current_data:
                        del current_data['plot_name']
                    if 'gap' in current_data:
                        del current_data['gap']
                    if 'area' in current_data:
                        del current_data['area']
                    if 'plot_id' in current_data:
                        del current_data['plot_id']
                    if 'gap_digits' in current_data:
                        del current_data['gap_digits']
                    MemoryCache.set(line_user_id, 'data', current_data)
                    return
                list_plot_names.append(current_data['plot_name'])
                MemoryCache.set(line_user_id, 'list_plot_names', list_plot_names)

                # Remove temporary data
                if 'plot_name' in current_data:
                    del current_data['plot_name']
                if 'gap' in current_data:
                    del current_data['gap']
                if 'area' in current_data:
                    del current_data['area']
                if 'plot_id' in current_data:
                    del current_data['plot_id']
                if 'gap_digits' in current_data:
                    del current_data['gap_digits']

                MemoryCache.set(line_user_id, 'data', current_data)
                # Move to plot selection
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_SELECTION)
                plot_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PLOT_SELECTION,
                    extra_context={
                        'plots': list_plot_names,
                        "allow_add_new_plot": True
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=plot_prompt
                )
            elif text in ["Edit", "แก้ไข"]:
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_NAME)
                add_plot_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_PLOT_NAME)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=add_plot_name_prompt
                )
            else:
                # Invalid input - re-send confirmation prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_PLOT_CERTIFICATE_IMAGE:
            # Invalid input because image should be in image event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for plot certificate image")

        elif current_state == ConversationState.AWAITING_ADDITIONAL_PLOTS:
            list_plot_names = MemoryCache.get(line_user_id, 'list_plot_names')
            if text in ["Yes, add more plots", "ใช่เพิ่มรายละเอียดแปลง"]:
                # Check if there are plots left to select
                # If not, go directly to add new plot
                if not list_plot_names:
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_PLOT_NAME)
                    add_plot_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_PLOT_NAME)
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=add_plot_name_prompt
                    )
                    return

                # Move to plot selection
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PLOT_SELECTION)
                user_role = MemoryCache.get(line_user_id, 'user_role')
                plot_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PLOT_SELECTION,
                    extra_context={
                        'plots': list_plot_names,
                        'allow_add_new_plot': True if user_role == 'farmer' else False
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=plot_prompt
                )
            elif text in ["No", "ไม่มี"]:
                # move to harvest date
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_HARVEST_DATE)
                harvest_date_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_HARVEST_DATE)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=harvest_date_prompt
                )
            else:
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_HARVEST_DATE:
            # Invalid input because harvesting date should be in postback event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for harvest date")

        elif current_state == ConversationState.AWAITING_VARIETY_SELECTION:
            if DURIAN_INFO.is_valid_variety(text):
                variety_value = DURIAN_INFO.mapping_variety_text_to_value(text)
                if 'selected_varieties' not in current_data:
                    current_data['selected_varieties'] = []
                current_data['selected_varieties'].append(variety_value)

                # Initialize variety_details if not exists
                if 'variety_details' not in current_data:
                    current_data['variety_details'] = {}
                current_data['variety_details'][variety_value] = {
                    'id': DURIAN_INFO.mapping_variety_value_to_id(variety_value),
                    'grades_weights': {},
                    'grade_value_to_id': {},
                    'total_weight': 0,
                    'variety_name': None
                }

                # If variety is "other", ask for variety name
                if variety_value == "other":
                    MemoryCache.set(line_user_id, 'data', current_data)
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADD_VARIETY_NAME)
                    add_variety_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADD_VARIETY_NAME)
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=add_variety_name_prompt
                    )
                    return

                # Move to ask blooming date per variety
                MemoryCache.set(line_user_id, 'data', current_data)
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY)
                if variety_value == "other":
                    variety_name = current_data['variety_details']['other']['variety_name']
                else:
                    variety_name = DURIAN_INFO.mapping_variety_value_to_text(variety_value, current_user_language)
                harvest_datetime = convert_to_datetime(current_data['harvest_date'])
                initial_date = (harvest_datetime - timedelta(days=DURIAN_INFO.get_variety_by_value(variety_value).flower_blooming_duration)).strftime('%Y-%m-%d')
                blooming_date_per_variety_prompt = create_harvest_question_response(
                    current_user_language, 
                    ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY,
                    extra_context={'current_variety': variety_name, 'initial_date': initial_date}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=blooming_date_per_variety_prompt
                )
            else:
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_ADD_VARIETY_NAME:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Variety name should not be blank")
            if len(text.strip()) >= 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            current_data['variety_details']['other']['variety_name'] = text.strip()

            # Move to ask blooming date per variety
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY)
            harvest_datetime = convert_to_datetime(current_data['harvest_date'])
            initial_date = (harvest_datetime - timedelta(days=DURIAN_INFO.get_variety_by_value("other").flower_blooming_duration)).strftime('%Y-%m-%d')
            blooming_date_per_variety_prompt = create_harvest_question_response(
                current_user_language, 
                ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY,
                extra_context={'current_variety': text.strip(), 'initial_date': initial_date}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=blooming_date_per_variety_prompt
            )

        elif current_state == ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY:
            if text in ["Skip", "ข้าม"]:
                # Move to ask grade and weight per variety
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY)

                # Create prompt asking for grade and weight
                current_variety = current_data['selected_varieties'][-1]
                current_available_grades = DURIAN_INFO.get_grade_label_by_variety_value(current_variety, current_user_language) or []
                # Save current available grades to cache
                MemoryCache.set(line_user_id, 'current_available_grades', current_available_grades)
                if current_variety == "other":
                    variety_name = current_data['variety_details']['other']['variety_name']
                else:
                    variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language)
                prompt_ask_grade_variety = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY,
                    extra_context={
                        'current_variety': variety_name,
                        'grades': current_available_grades
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=[prompt_ask_grade_variety]
                )
                return
            # Invalid input - Handle blooming date through postback
            raise TypeError(f"{current_state}: Invalid type of input for blooming date")

        elif current_state == ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Grade should not be blank")

            current_variety = current_data['selected_varieties'][-1]

            grade_value = DURIAN_INFO.get_grade_value(current_variety, text.strip())

            # Check if grade exists for current variety
            if not DURIAN_INFO.is_valid_grade(current_variety, text.strip()):
                # Invalid input - stay in current state and re-show grade selection without error message
                raise ValueError(f"{current_state}: Invalid selected option: {text.strip()}")
            
            # Store selected grade and ask for weight
            current_data['current_grade'] = grade_value
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_WEIGHT_PER_VARIETY)
            
            weight_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_WEIGHT_PER_VARIETY,
                extra_context={
                    'grade': text.strip()
                }
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=weight_prompt
            )

        elif current_state == ConversationState.AWAITING_WEIGHT_PER_VARIETY:
            try:
                # Check if weight contains at most 1 decimal place
                if '.' in text:
                    decimal_part = text.split('.')[1]
                    if len(decimal_part) > 1:
                        # Send message down to 1 decimal place
                        weight_prompt = TextMessage(text=get_displayed_texts("Invalid decimal place for weight", current_user_language)[0])
                        send_line_message(
                            line_user_id=line_user_id,
                            reply_token=event.reply_token,
                            message={"type": "reply", "message": weight_prompt}
                        )
                        return
                weight = float(text)
                if weight < 1.0 or weight > 9999999.9:
                    raise ValueError(f"{current_state}: Invalid weight value")

                current_variety = current_data['selected_varieties'][-1]
                grade_value = current_data['current_grade']
                
                # Store grade and weight per variety
                if grade_value in current_data['variety_details'][current_variety]['grades_weights']:
                    # If grade already exists, subtract previous weight
                    current_data['variety_details'][current_variety]['total_weight'] -= current_data['variety_details'][current_variety]['grades_weights'][grade_value]
                    current_data['total_weight'] -= current_data['variety_details'][current_variety]['grades_weights'][grade_value]

                current_data['variety_details'][current_variety]['grades_weights'][grade_value] = weight
                current_data['variety_details'][current_variety]['grade_value_to_id'][grade_value] = DURIAN_INFO.mapping_grade_value_to_id(current_variety, grade_value)
                current_data['variety_details'][current_variety]['total_weight'] += weight

                # Store total weight
                current_data['total_weight'] = current_data.get('total_weight', 0.0) + weight
                
                MemoryCache.set(line_user_id, 'data', current_data)

                # Check if there are more grades to add
                selected_grades = current_data['variety_details'][current_variety]['grades_weights'].keys()
                current_available_grades = MemoryCache.get(line_user_id, 'current_available_grades')
                if len(selected_grades) == len(current_available_grades):
                    # Move to ask grade and weight confirmation
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_CONFIRMATION)
                    if current_variety == "other":
                        variety_name = current_data['variety_details']['other']['variety_name']
                    else:
                        variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language)
                    grade_weight_confirm_prompt = create_harvest_question_response(
                        current_user_language,
                        ConversationState.AWAITING_GRADE_WEIGHT_CONFIRMATION,
                        extra_context={
                            'current_variety': current_variety,
                            'variety_name': variety_name,
                            'grades_weights': current_data['variety_details'][current_variety]['grades_weights'],
                            'total_weight': current_data['variety_details'][current_variety]['total_weight']
                        }
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=grade_weight_confirm_prompt
                    )
                    return

                # Move to ask add additional grades
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADDITIONAL_GRADES)
                
                prompt_ask_additional_grade = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_ADDITIONAL_GRADES
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=prompt_ask_additional_grade
                )
            except ValueError:
                weight_prompt = TextMessage(
                    text=get_displayed_texts("Invalid weight", current_user_language)[0]
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message={"type": "reply", "message": weight_prompt}
                )

        elif current_state == ConversationState.AWAITING_ADDITIONAL_GRADES:
            current_variety = current_data['selected_varieties'][-1]
            if current_variety == "other":
                variety_name = current_data['variety_details']['other']['variety_name']
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language)
            
            if text in ["Yes, add more grades", "ใช่เพิ่มรายละเอียดเกรด"]:
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY)
                current_available_grades = MemoryCache.get(line_user_id, 'current_available_grades')
                remain_grades: list[str] = []
                for grade_label in current_available_grades:
                    grade_value = DURIAN_INFO.get_grade_value(current_variety, grade_label)
                    if grade_value not in current_data['variety_details'][current_variety]['grades_weights']:
                        remain_grades.append(grade_label)
                grade_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY,
                    extra_context={
                        'current_variety': variety_name,
                        'grades': remain_grades
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=grade_prompt
                )
            elif text in ["No", "ไม่มี"]:
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_CONFIRMATION)
                confirm_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_GRADE_WEIGHT_CONFIRMATION,
                    extra_context={
                        'current_variety': current_variety,
                        'variety_name': variety_name,
                        'grades_weights': current_data['variety_details'][current_variety]['grades_weights'],
                        'total_weight': current_data['variety_details'][current_variety]['total_weight']
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=confirm_prompt
                )
            else:
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_GRADE_WEIGHT_CONFIRMATION:
            if text in ["Confirm", "ยืนยัน"]:
                # Move to next variety or next step
                MemoryCache.set(line_user_id, 'data', current_data)

                if len(current_data['selected_varieties']) == len(DURIAN_INFO.get_varieties()) :
                    # All varieties processed, move to ask durian photo stage
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_DURIAN_PHOTO)
                    ask_durian_photo_prompt = create_harvest_question_response(
                        current_user_language,
                        ConversationState.AWAITING_DURIAN_PHOTO,
                        extra_context={'allow_skip': True}
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=ask_durian_photo_prompt
                    )
                else:
                    # Move to ask add additional varieties
                    MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_ADDITIONAL_VARIETIES)
                    more_varieties_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_ADDITIONAL_VARIETIES)
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=more_varieties_prompt
                    )

            elif text in ["Edit", "แก้ไข"]:
                # Re-ask grade/weight for current variety
                current_variety = current_data['selected_varieties'][-1]
                if current_variety == "other":
                    variety_name = current_data['variety_details']['other']['variety_name']
                else:
                    variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language)
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY)

                # Create prompt asking for grade and weight
                selected_grades = current_data['variety_details'][current_variety]['grades_weights'].keys()
                current_available_grades: list[str] = []
                for grade_value in selected_grades:
                    current_available_grades.append(DURIAN_INFO.mapping_grade_value_to_text(current_variety, grade_value, current_user_language))
                # Save available grades to cache
                MemoryCache.set(line_user_id, 'current_available_grades', current_available_grades)
                prompt_ask_grade_variety = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY,
                    extra_context={
                        'current_variety': variety_name,
                        'grades': current_available_grades
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=prompt_ask_grade_variety
                )
            else:
                # Invalid input - re-send confirmation prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_ADDITIONAL_VARIETIES:
            if text in ["Yes, add more varieties", "ใช่เพิ่มรายละเอียดทุเรียน"]:
                valid_varieties = []
                for variety in DURIAN_INFO.get_varieties():
                    if variety.value not in current_data['selected_varieties']:
                        valid_varieties.append(variety.label.en if current_user_language == 'english' else variety.label.th)
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VARIETY_SELECTION)
                variety_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_VARIETY_SELECTION,
                    extra_context={'varieties': valid_varieties}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=variety_prompt
                )
            elif text in ["No", "ไม่มี"]:
                # Move to ask durian photo stage
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_DURIAN_PHOTO)
                ask_durian_photo_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_DURIAN_PHOTO,
                    extra_context={'allow_skip': True}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=ask_durian_photo_prompt
                )
            else:
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_DURIAN_PHOTO:
            if text.lower() == "skip" or text == "ข้าม":
                process_after_uploading_durian_photo(line_user_id, current_data, current_user_language, event.reply_token)
                return

            # Invalid input - re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for durian photo")

        elif current_state == ConversationState.AWAITING_CUTTER_SELECTION:
            # Invalid input because cutter selection should be in postback event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for cutter selection")

        elif current_state == ConversationState.AWAITING_CUTTER_NAME:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Cutter name should not be blank")

            # Check if cutter name is not longer than 100 characters
            if len(text.strip()) > 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            current_data['cutter_name'] = text.strip()
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_REGISTRATION)
            ask_cutter_registration_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_REGISTRATION)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=ask_cutter_registration_prompt
            )

        elif current_state == ConversationState.AWAITING_CUTTER_REGISTRATION:
            if text in ["Already registered", "ขึ้นทะเบียนแล้ว"]:
                current_data['cutter_registration'] = True
                MemoryCache.set(line_user_id, 'data', current_data)

                # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_REGISTRATION_NUMBER)
                # ask_cutter_registration_number_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_REGISTRATION_NUMBER)
                # send_line_message(
                #     line_user_id=line_user_id,
                #     reply_token=event.reply_token,
                #     message=ask_cutter_registration_number_prompt
                # )

                # Move to ask cutter photo
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_PHOTO)
                cutter_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_PHOTO)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=cutter_photo_prompt
                )
            elif text in ["I do not know", "ไม่ทราบ"]:
                current_data['cutter_registration'] = False
                current_data['cutter_registration_number'] = None
                MemoryCache.set(line_user_id, 'data', current_data)
                # Move to ask cutter photo
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_PHOTO)
                cutter_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_PHOTO)
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=cutter_photo_prompt
                )
                # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_PHOTO)
                # vehicle_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_PHOTO)
                # send_line_message(
                #     line_user_id=line_user_id,
                #     reply_token=event.reply_token,
                #     message=vehicle_photo_prompt
                # )
            else:
                # Invalid input - re-ask registration status
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_CUTTER_REGISTRATION_NUMBER:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Cutter registration number should not be blank")

            # Check if cutter registration number is not longer than 100 characters
            if len(text.strip()) > 100:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            current_data['cutter_registration_number'] = text.strip()
            MemoryCache.set(line_user_id, 'data', current_data)
            # Move to ask cutter photo
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_PHOTO)
            cutter_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_PHOTO)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=cutter_photo_prompt
            )
            # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_PHOTO)
            # vehicle_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_PHOTO)
            # send_line_message(
            #     line_user_id=line_user_id,
            #     reply_token=event.reply_token,
            #     message=vehicle_photo_prompt
            # )

        elif current_state == ConversationState.AWAITING_CUTTER_PHOTO:
            # Invalid input because cutter photo should be in image event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for cutter photo")

        elif current_state == ConversationState.AWAITING_VEHICLE_PHOTO:
            # Invalid input because vehicle photo should be in image event. Stay in current state and re-send prompt
            raise TypeError(f"{current_state}: Invalid type of input for vehicle photo")

        elif current_state == ConversationState.AWAITING_VEHICLE_REGISTRATION_NUMBER:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Vehicle registration number should not be blank")

            # Check if vehicle registration number is not longer than 20 characters
            if len(text.strip()) > 20:
                raise CharacterLimitExceededError(value=text.strip(), step=current_state)

            # # Check if vehicle registration number is a valid Thailand vehicle registration number
            # if re.match(r'^[0-9]?[\u0E00-\u0E7F]{2} \d{1,4}( [\u0E00-\u0E7F]+)?$', text.strip()) is None:
            #     msg_invalid_registration_number = get_displayed_texts("Invalid vehicle registration number", current_user_language)[0]
            #     send_line_message(
            #         line_user_id=line_user_id,
            #         reply_token=event.reply_token,
            #         message={"type": "reply", "message": TextMessage(text=msg_invalid_registration_number)}
            #     )
            #     return

            # Check if vehicle registration number contains any special characters (allow hyphen and space in middle)
            if re.match(r'^[A-Za-z0-9\u0E00-\u0E7F\-](?:[A-Za-z0-9\u0E00-\u0E7F\- ]*[A-Za-z0-9\u0E00-\u0E7F\-])?$', text.strip()) is None:
                raise SpecialCharacterError(value=text.strip(), step=current_state)

            current_data['vehicle_registration_number'] = text.strip()
            MemoryCache.set(line_user_id, 'data', current_data)
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VEHICLE_REGISTRATION_PROVINCE)
            vehicle_registration_province_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_VEHICLE_REGISTRATION_PROVINCE)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=vehicle_registration_province_prompt
            )

        elif current_state == ConversationState.AWAITING_VEHICLE_REGISTRATION_PROVINCE:
            if not text.strip():
                # Invalid input - stay in current state and re-send prompt
                raise ValueError(f"{current_state}: Vehicle registration province should not be blank")

            # Check if vehicle registration province is a valid Thailand province
            if not DURIAN_INFO.is_valid_province(text.strip()):
                invalid_province_prompt = {
                    "type": "reply",
                    "message": TextMessage(text=get_displayed_texts("Invalid province", current_user_language)[0])
                }
                last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=[invalid_province_prompt, last_sent_msg]
                )
                return

            current_data['vehicle_registration_province'] = text.strip()
            send_cutter_info(line_user_id, current_data, current_user_language)

            MemoryCache.set(line_user_id, 'data', current_data)

            # Move to send/draft state
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_SEND_OR_DRAFT)
            send_or_draft_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_SEND_OR_DRAFT)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=send_or_draft_prompt
            )

            # # Move to packing house selection
            # ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
            # list_packing_houses = get_list_packing_houses(ptp_user_id)
            # displayed_packing_houses = []
            # mapping_packing_house_to_id = {}
            # for packing_house in list_packing_houses:
            #     displayed_packing_houses.append(packing_house['name'])
            #     mapping_packing_house_to_id[packing_house['name']] = packing_house['id']
            # # Save mapping packing house to id and displayed packing houses for later use
            # MemoryCache.set(line_user_id, 'displayed_packing_houses', displayed_packing_houses)
            # MemoryCache.set(line_user_id, 'mapping_packing_house_to_id', mapping_packing_house_to_id)
            # # Move to packing house selection
            # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PACKING_HOUSE)
            # packing_house_prompt = create_harvest_question_response(
            #     current_user_language,
            #     ConversationState.AWAITING_PACKING_HOUSE,
            #     extra_context={'packing_houses': displayed_packing_houses}
            # )
            # send_line_message(
            #     line_user_id=line_user_id,
            #     reply_token=event.reply_token,
            #     message=packing_house_prompt
            # )
            
        elif current_state == ConversationState.AWAITING_SEND_OR_DRAFT:
            if text in ["Send to packing house", "ส่งให้ล้งที่รับซื้อ"]:
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                list_packing_houses = get_list_packing_houses(ptp_user_id)
                displayed_packing_houses = []
                mapping_packing_house_to_id = {}
                for packing_house in list_packing_houses:
                    displayed_packing_houses.append(packing_house['name'])
                    mapping_packing_house_to_id[packing_house['name']] = packing_house['id']
                # Save mapping packing house to id and displayed packing houses for later use
                MemoryCache.set(line_user_id, 'displayed_packing_houses', displayed_packing_houses)
                MemoryCache.set(line_user_id, 'mapping_packing_house_to_id', mapping_packing_house_to_id)
                # Move to packing house selection
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_PACKING_HOUSE)
                packing_house_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_PACKING_HOUSE,
                    extra_context={'packing_houses': displayed_packing_houses}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=packing_house_prompt
                )
            elif text in ["Save as draft", "บันทึกเป็นฉบับร่าง"]:
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                user_role = MemoryCache.get(line_user_id, 'user_role')
                response = send_durian_event_harvest(ptp_user_id, user_role, current_data)
                current_data['event_id'] = response['data']['id']
                DATABASE.save_durian_record(current_data)
                user_line_jwt_token = get_line_jwt_token(line_user_id)
                view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={current_data['event_id']}&channel=line-app&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
                save_as_draft_prompt = create_harvest_question_response(
                    current_user_language,
                    "SAVE_AS_DRAFT",
                    extra_context={'view_detail_link': view_detail_link}
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=save_as_draft_prompt
                )
                MemoryCache.delete_all_except(line_user_id, ['language'])
            else:
                # Invalid input - re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

        elif current_state == ConversationState.AWAITING_PACKING_HOUSE:
            list_packing_houses: dict = MemoryCache.get(line_user_id, 'displayed_packing_houses')
            if text not in list_packing_houses:
                # Invalid input - re-send prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")
            else:
                # Save packing house and move to final confirmation
                current_data['packing_house'] = text
                current_data['packing_house_id'] = MemoryCache.get(line_user_id, 'mapping_packing_house_to_id')[text]

                # Send to ptp controller as draft event
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                user_role = MemoryCache.get(line_user_id, 'user_role')
                response = send_durian_event_harvest(ptp_user_id, user_role, current_data)

                # Get event id
                current_data['event_id'] = response['data']['id']
                # Get product info
                product_id = response['data']['product_id']
                product_info = get_product_info(product_id, ptp_user_id)
                current_data['batchlot'] = product_info['batchlot']

                # Send durian info to user for confirmation
                send_durian_info(line_user_id, current_data, current_user_language)

                MemoryCache.set(line_user_id, 'data', current_data)
                DATABASE.save_durian_record(current_data)

                # Move to final confirmation
                MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_FINAL_CONFIRMATION)
                user_line_jwt_token = get_line_jwt_token(line_user_id)
                view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={response['data']['id']}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}"
                final_confirmation_prompt = create_harvest_question_response(
                    current_user_language,
                    ConversationState.AWAITING_FINAL_CONFIRMATION,
                    extra_context={
                        'event_id': response['data']['id'],
                        'view_detail_link': view_detail_link
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=final_confirmation_prompt
                )

                # Clear cache
                MemoryCache.delete_all_except(line_user_id, ['language'])

        elif current_state == ConversationState.AWAITING_FINAL_CONFIRMATION:
            if text in ["Confirm and send", "ยืนยัน และส่งข้อมูล", "Edit information", "แก้ไขข้อมูล", "Save as draft", "เก็บไว้ก่อน"]:
                ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
                user_line_jwt_token = get_line_jwt_token(line_user_id)
                view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={current_data['event_id']}&channel=line-app&user_line_jwt_token={user_line_jwt_token}&line_bot_id={LINE_BOT_ID}"
                
                if text in ["Confirm and send", "ยืนยัน และส่งข้อมูล"]:
                    # Send to ptp controller as published event
                    update_durian_event_harvest(ptp_user_id, current_data['event_id'], {"status": "published"})

                    confirm_and_send_prompt = create_harvest_question_response(
                        current_user_language,
                        "CONFIRM_AND_SEND",
                        extra_context={
                            'packing_house': current_data['packing_house'],
                            'view_detail_link': view_detail_link
                        }
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=confirm_and_send_prompt
                    )

                    # Clear cache
                    MemoryCache.delete_all_except(line_user_id, ['language'])
                elif text in ["Edit information", "แก้ไขข้อมูล"]:
                    # Save as draft first and get edit link
                    # response = send_durian_event_harvest(user_access_token, current_data)
                    # DATABASE.save_durian_record(current_data)

                    edit_prompt = create_harvest_question_response(
                        current_user_language,
                        "EDIT_AND_SEND",
                        extra_context={
                            'harvest_lot_name': current_data['harvest_lot_name'],
                            'view_detail_link': view_detail_link
                        }
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=edit_prompt
                    )

                    # Clear cache
                    MemoryCache.delete_all_except(line_user_id, ['language'])
                elif text in ["Save as draft", "เก็บไว้ก่อน"]:
                    # Save as draft
                    # send_durian_event_harvest(user_access_token, current_data)
                    # DATABASE.save_durian_record(current_data)

                    # Clear cache
                    MemoryCache.delete_all_except(line_user_id, ['language'])
                    save_as_draft_prompt = create_harvest_question_response(
                        current_user_language,
                        "SAVE_AS_DRAFT",
                        extra_context={
                            'view_detail_link': view_detail_link
                        }
                    )
                    send_line_message(
                        line_user_id=line_user_id,
                        reply_token=event.reply_token,
                        message=save_as_draft_prompt
                    )
            else:
                # Invalid input - re-send confirmation prompt
                raise ValueError(f"{current_state}: Invalid selected option: {text}")

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error processing text: {str(e)}")

        if isinstance(e, (ValueError, TypeError)):
            msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
            sent_invalid_input_msg = {
                "type": "reply",
                "message": TextMessage(text=msg_invalid_input)
            }
            last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=[sent_invalid_input_msg, last_sent_msg]
            )
            return

        elif isinstance(e, CharacterLimitExceededError):
            # Send character limit exceeded error message
            msg_error = get_displayed_texts("Character limit exceeded", current_user_language)[0]
            if current_state == ConversationState.AWAITING_VEHICLE_REGISTRATION_NUMBER:
                msg_error = msg_error.format(char_limit=20)
            else:
                msg_error = msg_error.format(char_limit=100)
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": msg}
            )
            return
        elif isinstance(e, DataExistsError):
            # Send data exists error message
            msg_error = get_displayed_texts("Data already exists", current_user_language)[0]
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": msg}
            )
            return

        elif isinstance(e, SpecialCharacterError):
            # Send special character error message
            msg_error = get_displayed_texts("Input contains special characters", current_user_language)[0]
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": msg}
            )
            return

        # Send error message to user
        msg_error = get_displayed_texts("Error processing text message", current_user_language)[0]
        msg = TextMessage(text=msg_error)
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": msg}
        )