import traceback
from linebot.v3.webhooks import Message<PERSON>vent
from linebot.v3.messaging import TextMessage

from service.ptp_controller_service import get_farmer_cutter_info, get_line_jwt_token
from utils.config import ENV, LINE_BOT_ID, M_WEB_APP_URL
from .helper import DATAB<PERSON><PERSON>, get_user_language, show_logout_button, show_relogin_button
from utils.memory_cache import MemoryCache
from service.line_service import send_line_message
from utils.language_mapping import get_displayed_texts
from utils.line_utils import create_line_button_message, create_selection_buttons
from utils.logger import logger
from utils.durian_info import DURIAN_INFO
from state_machine.harvest_flow_manager import HarvestFlowManager

"""
Handles text messages sent by users.
Checks if the user is logged in and processes the message accordingly.
"""
def handle_text_message(event: MessageEvent):
    line_user_id = event.source.user_id
    text = event.message.text

    # Get user preferred language
    if ENV == 'dev':
        if not get_user_language(line_user_id) and text.lower() not in ["english", "thailand"]:
            send_line_message(
                line_user_id=line_user_id,
                message={"type": "flex", "message": create_selection_buttons(title="Please select your preferred language:", items=['English', 'Thailand'])}
            )
            MemoryCache.set(line_user_id, 'language', None)
            return

    # Set user preferred language
    if text.lower() in ["english", "thailand"]:
        MemoryCache.set(line_user_id, 'language', text.lower())
        DATABASE.save_user_language(line_user_id, text.lower())
        send_line_message(
            reply_token=event.reply_token,
            message={
                "type": "reply",
                "message": [
                    TextMessage(text=f"Language set to {text}."),
                    TextMessage(text=get_displayed_texts("Welcome message", MemoryCache.get(line_user_id, 'language'))[0])
                ]
            }
        )
        return

    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'

    if text in ["Record Durian", "Harvest", "Add durian harvesting information", "เพิ่มข้อมูลการเก็บเกี่ยวทุเรียน", "เก็บเกี่ยว", "จะตัดทุเรียนแล้ว", "วันนี้มีตัด", "I want to log durians today", "Record harvest", "บันทึกทุเรียน", "เพิ่มการเก็บเกี่ยวใหม่", "Add new record"]:
        if not DURIAN_INFO.is_varieties_available():
            msg = get_displayed_texts("Out of service", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=msg)}
            )
            return
        # Check user role
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_info = get_farmer_cutter_info(ptp_user_id)
        user_role = user_info['data']['profile']['role']

        if user_role not in ['farmer', 'cutter']:
            permission_msg = get_displayed_texts("Permission denied", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=permission_msg)}
            )
            return

        # Use state machine to start harvest flow
        harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
        harvest_manager.start_harvest_flow(user_role, event.reply_token)
        return

    if text in ["View harvest history", "ดูประวัติการเก็บเกี่ยวทุเรียน", "ประวัติศาสตร์", "View durian harvest history", "ประวัติการเก็บเกี่ยวทุเรียน"]:
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_info = get_farmer_cutter_info(ptp_user_id)
        user_role = user_info['data']['profile']['role']
        if user_role not in ['farmer', 'cutter']:
            deny_view_history_msg = get_displayed_texts("Deny view history", current_user_language)[0]
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message={"type": "reply", "message": TextMessage(text=deny_view_history_msg)}
            )
            return

        view_history_link = f"{M_WEB_APP_URL}/main?channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={get_line_jwt_token(line_user_id)}"
        view_history_msg = get_displayed_texts("View harvest history", current_user_language)[0]
        view_history_prompt = {
            "type": "flex",
            "message": create_line_button_message(
                displayed_message=view_history_msg,
                btn_title="View harvest history" if current_user_language == 'english' else "ดูประวัติการเก็บเกี่ยว",
                uri=view_history_link
            )
        }
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=view_history_prompt
        )
        return

    if text.lower() in ["logout", "sign out", "log out", "ออกจากระบบ"]:
        show_logout_button(line_user_id, current_user_language)
        return

    if text.lower() in ["relogin", "re-login", "เข้าสู่ระบบใหม่อีกครั้ง", "login", "เข้าสู่ระบบ"]:
        show_relogin_button(line_user_id, current_user_language)
        return

    current_state = MemoryCache.get(line_user_id, 'state')

    if not current_state:
        msg = get_displayed_texts("Welcome message", current_user_language)[0]
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": TextMessage(text=msg)}
        )
        return

    ## Process event harvesting using state machine
    try:
        # Check if user is in harvest flow (has a state that starts with AWAITING_)
        if current_state and current_state.startswith('AWAITING_'):
            # Use HarvestFlowManager for all harvest states
            harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
            harvest_manager.process_text_input(text, event.reply_token)
            return

    except Exception as e:
        # All harvest flow exceptions are now handled in HarvestFlowManager
        # This is just a fallback for non-harvest flow errors
        traceback.print_exc()
        logger.error(f"Error processing text: {str(e)}")

        # Send generic error message
        msg_error = get_displayed_texts("Error processing text message", current_user_language)[0]
        msg = TextMessage(text=msg_error)
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message={"type": "reply", "message": msg}
        )