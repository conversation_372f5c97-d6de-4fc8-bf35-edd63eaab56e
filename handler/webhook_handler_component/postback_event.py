# Removed unused imports
from linebot.v3.webhooks import PostbackEvent
from urllib.parse import parse_qsl
from linebot.v3.messaging import TextMessage

from service.ptp_controller_service import get_durian_event_harvest, get_line_jwt_token, update_durian_event_harvest
from utils.config import ENV, LINE_BOT_ID, M_WEB_APP_URL
from .helper import DATABASE
from utils.memory_cache import MemoryCache
from utils.language_mapping import get_displayed_texts
from utils.line_utils import create_line_button_message, create_harvest_question_response
from service.line_service import send_line_message
from state_machine.harvest_flow_manager import HarvestFlowManager

def handle_postback(event: PostbackEvent):
    line_user_id = event.source.user_id
    data = dict(parse_qsl(event.postback.data))
    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'
    current_state = MemoryCache.get(line_user_id, 'state')

    # Check if we should use the new HarvestFlowManager for harvest-related postbacks
    harvest_actions = ['harvest_date', 'blooming_date_per_variety', 'select_cutter', 'add_new_cutter']
    if current_state and current_state.startswith('AWAITING_') and data.get('action') in harvest_actions:
        harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
        harvest_manager.process_postback_input(event.postback.data, event.reply_token)
        return

    # Handle non-harvest related postbacks below
    # (harvest-related postbacks are now handled by HarvestFlowManager)

    if data.get('action') == 'confirm_send':
        event_id = data.get('event_id')
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_line_jwt_token = get_line_jwt_token(line_user_id)
        # Get harvest event detail
        harvest_event = get_durian_event_harvest(ptp_user_id, event_id)
        if harvest_event is None:
            # If the event is not found, send an error message
            harvest_event_not_found_msg = {
                "type": "reply",
                "message": TextMessage(text=get_displayed_texts("Harvest record not found", current_user_language)[0])
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=harvest_event_not_found_msg
            )
            return

        if harvest_event.get('status') != 'draft':
            # If the event is not in draft status, send a message that it was processed
            event_processed_msg = get_displayed_texts("Event was processed", current_user_language)[0]
            btn_title = "View detail" if current_user_language == 'english' else "ดูรายละเอียด"
            user_line_jwt_token = get_line_jwt_token(line_user_id)
            dont_allow_confirm_send_prompt = {
                "type": "flex",
                "message": create_line_button_message(
                    displayed_message=event_processed_msg,
                    btn_title=btn_title,
                    uri=f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}",
                )
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=dont_allow_confirm_send_prompt
            )
            return

        # Send to ptp controller as published event
        update_durian_event_harvest(ptp_user_id, event_id, {"status": "published"})

        user_line_jwt_token = get_line_jwt_token(line_user_id)
        view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}"
        confirm_and_send_prompt = create_harvest_question_response(
            current_user_language,
            "CONFIRM_AND_SEND",
            extra_context={
                'view_detail_link': view_detail_link
            }
        )
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=confirm_and_send_prompt
        )
        return

    elif data.get('action') == 'save_as_draft':
        event_id = data.get('event_id')
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        # Check if the event has been published
        # If it has been published, do not allow saving as draft
        harvest_event = get_durian_event_harvest(ptp_user_id, event_id)

        # If the event is not found, send an error message
        if harvest_event is None:
            # If the event is not found, send an error message
            harvest_event_not_found_msg = {
                "type": "reply",
                "message": TextMessage(text=get_displayed_texts("Harvest record not found", current_user_language)[0])
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=harvest_event_not_found_msg
            )
            return

        # Get user line jwt token
        user_line_jwt_token = get_line_jwt_token(line_user_id)

        # Check if the event is not in draft status
        # If the event is not in draft status, do not allow saving as draft
        if harvest_event.get('status') != 'draft':
            # If the event is not in draft status, send an error message
            dont_allow_save_as_draft_msg = get_displayed_texts("Cannot save as draft", current_user_language)[0]
            btn_title = "View detail" if current_user_language == 'english' else "ดูรายละเอียด"
            dont_allow_save_as_draft_prompt = {
                "type": "flex",
                "message": create_line_button_message(
                    displayed_message=dont_allow_save_as_draft_msg,
                    btn_title=btn_title,
                    uri=f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}",
                )
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=dont_allow_save_as_draft_prompt
            )
            return

        # The event is in draft first, so we do not need to update the status
        # We just need to send the message to the user to confirm saving as draft
        # and provide the link to view the details
        view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}"
        save_as_draft_prompt = create_harvest_question_response(
            current_user_language,
            "SAVE_AS_DRAFT",
            extra_context={
                'view_detail_link': view_detail_link
            }
        )
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=save_as_draft_prompt
        )
        return

    # Invalid input - re-send previous prompt if exists
    msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
    sent_invalid_input_msg = {
        "type": "reply",
        "message": TextMessage(text=msg_invalid_input)
    }
    last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
    if last_sent_msg is not None:
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[sent_invalid_input_msg, last_sent_msg]
        )

    send_line_message(
        line_user_id=line_user_id,
        reply_token=event.reply_token,
        message=sent_invalid_input_msg
    )
    return