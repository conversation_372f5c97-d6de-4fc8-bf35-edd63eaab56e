from datetime import datetime, timezone, timedelta
from linebot.v3.webhooks import PostbackEvent
from urllib.parse import parse_qsl
from linebot.v3.messaging import TextMessage

from service.ptp_controller_service import get_durian_event_harvest, get_farmer_cutter_info, get_line_jwt_token, update_durian_event_harvest
from utils.config import ENV, LINE_BOT_ID, M_WEB_APP_URL
from .helper import DATABASE, ConversationState
from utils.memory_cache import MemoryCache
from utils.language_mapping import get_displayed_texts
from utils.line_utils import create_line_button_message, create_harvest_question_response
from service.line_service import send_line_message, send_flex_message
from utils.flex_message import (
    create_flex_message_ask_durian_photo
)
from utils.logger import logger
from utils.durian_info import DURIAN_INFO
from utils.time_util import convert_to_datetime, thai_to_gregorian, format_date
from state_machine.harvest_flow_manager import Harvest<PERSON>lowManager

def handle_postback(event: PostbackEvent):
    line_user_id = event.source.user_id
    data = dict(parse_qsl(event.postback.data))
    current_user_language = MemoryCache.get(line_user_id, 'language') if ENV == 'dev' else 'thailand'
    current_data = MemoryCache.get(line_user_id, 'data') or {}
    current_state = MemoryCache.get(line_user_id, 'state')
    # Set timezone to GMT+7
    tz_offset = timedelta(hours=7)

    # Check if we should use the new HarvestFlowManager for harvest-related postbacks
    if current_state and current_state.startswith('AWAITING_') and data.get('action') in ['harvest_date', 'blooming_date_per_variety']:
        harvest_manager = HarvestFlowManager(line_user_id, current_user_language)
        harvest_manager.process_postback_input(event.postback.data, event.reply_token)
        return

    if data.get('action') == 'harvest_date':
        if current_state == ConversationState.AWAITING_HARVEST_DATE:
            harvest_date = event.postback.params['datetime']
            # send selected date prompt
            harvest_date_info_prompt = {
                "type": "reply",
                "message": TextMessage(text=f"Harvest date: {format_date(harvest_date, current_user_language)}"
                                        if current_user_language == 'english'
                                            else f"วันที่เก็บเกี่ยว: {format_date(harvest_date, current_user_language)}")
            }

            # Check if the harvest date is in the future
            # If yes, send an error message
            # and do not proceed with the rest of the code
            harvest_date_object = datetime.strptime(harvest_date, '%Y-%m-%dT%H:%M')
            if harvest_date_object.year > datetime.now().year + 500:
                harvest_date_object = thai_to_gregorian(harvest_date_object)
            harvest_date_object = harvest_date_object.replace(tzinfo=timezone(tz_offset))
            if harvest_date_object > datetime.now(tz=timezone(tz_offset)):
                invalid_harvest_date_prompt = {
                    "type": "reply",
                    "message": TextMessage(text=get_displayed_texts("Invalid harvest date", current_user_language)[0])
                }
                last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=[harvest_date_info_prompt, invalid_harvest_date_prompt, last_sent_msg]
                )
                return

            current_data['harvest_date'] = harvest_date
            MemoryCache.set(line_user_id, 'data', current_data)
            # MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_DURIAN_PHOTO)
            # ask_durian_photo_prompt = {
            #     "type": "flex",
            #     "message": create_flex_message_ask_durian_photo(current_user_language)
            # }
            # send_line_message(
            #     line_user_id=line_user_id,
            #     reply_token=event.reply_token,
            #     message=[harvest_date_info_prompt, ask_durian_photo_prompt]
            # )
            # Move to ask variety
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_VARIETY_SELECTION)
            variety_prompt = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_VARIETY_SELECTION,
                extra_context={'varieties': DURIAN_INFO.get_variety_labels(current_user_language)}
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=[harvest_date_info_prompt, variety_prompt]
            )
            return

    elif data.get('action') == 'blooming_date_per_variety':
        if current_state == ConversationState.AWAITING_BLOOMING_DATE_PER_VARIETY:
            current_variety = current_data['selected_varieties'][-1]
            blooming_date = event.postback.params['date']

            # send selected date prompt
            blooming_date_info_prompt = {
                "type": "reply",
                "message": TextMessage(text=f"Blooming date: {format_date(blooming_date, current_user_language)}"
                                         if current_user_language == 'english'
                                            else f"วันที่ดอกทุเรียนบาน: {format_date(blooming_date, current_user_language)}")
            }

            blooming_date_object = datetime.strptime(blooming_date, '%Y-%m-%d')
            if blooming_date_object.year > datetime.now().year + 500:
                blooming_date_object = thai_to_gregorian(blooming_date_object)
            # Check if the blooming date is before the harvest date
            harvest_date_object = datetime.strptime(current_data.get('harvest_date'), '%Y-%m-%dT%H:%M')
            if harvest_date_object.year > datetime.now().year + 500:
                harvest_date_object = thai_to_gregorian(harvest_date_object)
            harvest_date_object = harvest_date_object.replace(tzinfo=timezone(tz_offset))
            if blooming_date_object.date() >= harvest_date_object.date():
                invalid_blooming_date_prompt = {
                    "type": "reply",
                    "message": TextMessage(text=get_displayed_texts("Invalid blooming date", current_user_language)[0])
                }
                last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=[blooming_date_info_prompt, invalid_blooming_date_prompt, last_sent_msg]
                )
                return

            current_data['variety_details'][current_variety]['blooming_date'] = blooming_date
            MemoryCache.set(line_user_id, 'data', current_data)

            # Check if the blooming date is before the harvest date based on the flower blooming duration
            # If not, send a warning message to user to confirm if user want to continue
            # or select another blooming date
            current_variety_flower_blooming_duration = DURIAN_INFO.get_variety_by_value(current_variety).flower_blooming_duration
            if blooming_date_object.date() > harvest_date_object.date() - timedelta(days=current_variety_flower_blooming_duration):
                harvest_datetime = convert_to_datetime(current_data['harvest_date'])
                initial_date = (harvest_datetime - timedelta(days=DURIAN_INFO.get_variety_by_value(current_variety).flower_blooming_duration)).strftime('%Y-%m-%d')
                warning_blooming_date_prompt = create_harvest_question_response(
                    language=current_user_language,
                    conversation_state="WARNING_BLOOMING_DATE",
                    extra_context={
                        'variety_name': DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language),
                        'flower_blooming_duration': current_variety_flower_blooming_duration,
                        'initial_date': initial_date
                    }
                )
                send_line_message(
                    line_user_id=line_user_id,
                    reply_token=event.reply_token,
                    message=[blooming_date_info_prompt, warning_blooming_date_prompt]
                )
                return

            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY)

            # Create prompt asking for grade and weight
            current_available_grades = DURIAN_INFO.get_grade_label_by_variety_value(current_variety, current_user_language) or []
            # Save current available grades to cache
            MemoryCache.set(line_user_id, 'current_available_grades', current_available_grades)
            if current_variety == "other":
                variety_name = current_data['variety_details']['other']['variety_name']
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, current_user_language)
            prompt_ask_grade_variety = create_harvest_question_response(
                current_user_language,
                ConversationState.AWAITING_GRADE_WEIGHT_PER_VARIETY,
                extra_context={
                    'current_variety': variety_name,
                    'grades': current_available_grades
                }
            )
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=[blooming_date_info_prompt, prompt_ask_grade_variety]
            )
            return

    elif data.get('action') == 'select_cutter':
        if current_state == ConversationState.AWAITING_CUTTER_SELECTION:
            cutter_id = data.get('cutter_id')
            current_data['cutter_id'] = cutter_id
            cutter = get_farmer_cutter_info(cutter_id).get('data', {})
            if cutter:
                current_data['cutter_name'] = cutter.get('first_name') + ' ' + cutter.get('last_name')
                current_data['cutter_registration'] = cutter.get('profile', {}).get('metadata', {}).get('is_certified') or False
                current_data['cutter_registration_number'] = cutter.get('profile', {}).get('metadata', {}).get('license_number', '')
                current_data['cutter_profile_id'] = cutter.get('profile', {}).get('id', '')
            MemoryCache.set(line_user_id, 'data', current_data)
            # Move to ask cutter photo
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_PHOTO)
            cutter_photo_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_PHOTO)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=cutter_photo_prompt
            )
            return

    elif data.get('action') == 'add_new_cutter':
        if current_state == ConversationState.AWAITING_CUTTER_SELECTION:
            MemoryCache.set(line_user_id, 'state', ConversationState.AWAITING_CUTTER_NAME)
            cutter_name_prompt = create_harvest_question_response(current_user_language, ConversationState.AWAITING_CUTTER_NAME)
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=cutter_name_prompt
            )
            return

    elif data.get('action') == 'confirm_send':
        event_id = data.get('event_id')
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        user_line_jwt_token = get_line_jwt_token(line_user_id)
        # Get harvest event detail
        harvest_event = get_durian_event_harvest(ptp_user_id, event_id)
        if harvest_event is None:
            # If the event is not found, send an error message
            harvest_event_not_found_msg = {
                "type": "reply",
                "message": TextMessage(text=get_displayed_texts("Harvest record not found", current_user_language)[0])
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=harvest_event_not_found_msg
            )
            return

        if harvest_event.get('status') != 'draft':
            # If the event is not in draft status, send a message that it was processed
            event_processed_msg = get_displayed_texts("Event was processed", current_user_language)[0]
            btn_title = "View detail" if current_user_language == 'english' else "ดูรายละเอียด"
            user_line_jwt_token = get_line_jwt_token(line_user_id)
            dont_allow_confirm_send_prompt = {
                "type": "flex",
                "message": create_line_button_message(
                    displayed_message=event_processed_msg,
                    btn_title=btn_title,
                    uri=f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}",
                )
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=dont_allow_confirm_send_prompt
            )
            return

        # Send to ptp controller as published event
        update_durian_event_harvest(ptp_user_id, event_id, {"status": "published"})

        user_line_jwt_token = get_line_jwt_token(line_user_id)
        view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}"
        confirm_and_send_prompt = create_harvest_question_response(
            current_user_language,
            "CONFIRM_AND_SEND",
            extra_context={
                'view_detail_link': view_detail_link
            }
        )
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=confirm_and_send_prompt
        )
        return

    elif data.get('action') == 'save_as_draft':
        event_id = data.get('event_id')
        ptp_user_id = DATABASE.get_line_user(query={'line_user_id':line_user_id}).user_id
        # Check if the event has been published
        # If it has been published, do not allow saving as draft
        harvest_event = get_durian_event_harvest(ptp_user_id, event_id)

        # If the event is not found, send an error message
        if harvest_event is None:
            # If the event is not found, send an error message
            harvest_event_not_found_msg = {
                "type": "reply",
                "message": TextMessage(text=get_displayed_texts("Harvest record not found", current_user_language)[0])
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=harvest_event_not_found_msg
            )
            return

        # Get user line jwt token
        user_line_jwt_token = get_line_jwt_token(line_user_id)

        # Check if the event is not in draft status
        # If the event is not in draft status, do not allow saving as draft
        if harvest_event.get('status') != 'draft':
            # If the event is not in draft status, send an error message
            dont_allow_save_as_draft_msg = get_displayed_texts("Cannot save as draft", current_user_language)[0]
            btn_title = "View detail" if current_user_language == 'english' else "ดูรายละเอียด"
            dont_allow_save_as_draft_prompt = {
                "type": "flex",
                "message": create_line_button_message(
                    displayed_message=dont_allow_save_as_draft_msg,
                    btn_title=btn_title,
                    uri=f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}",
                )
            }
            send_line_message(
                line_user_id=line_user_id,
                reply_token=event.reply_token,
                message=dont_allow_save_as_draft_prompt
            )
            return

        # The event is in draft first, so we do not need to update the status
        # We just need to send the message to the user to confirm saving as draft
        # and provide the link to view the details
        view_detail_link = f"{M_WEB_APP_URL}/harvest_details?event-id={event_id}&channel=line-app&line_bot_id={LINE_BOT_ID}&user_line_jwt_token={user_line_jwt_token}"
        save_as_draft_prompt = create_harvest_question_response(
            current_user_language,
            "SAVE_AS_DRAFT",
            extra_context={
                'view_detail_link': view_detail_link
            }
        )
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=save_as_draft_prompt
        )
        return

    # Invalid input - re-send previous prompt if exists
    msg_invalid_input = get_displayed_texts("Invalid input", current_user_language)[0]
    sent_invalid_input_msg = {
        "type": "reply",
        "message": TextMessage(text=msg_invalid_input)
    }
    last_sent_msg = MemoryCache.get(line_user_id, "last_sent_msg")
    if last_sent_msg is not None:
        send_line_message(
            line_user_id=line_user_id,
            reply_token=event.reply_token,
            message=[sent_invalid_input_msg, last_sent_msg]
        )

    send_line_message(
        line_user_id=line_user_id,
        reply_token=event.reply_token,
        message=sent_invalid_input_msg
    )
    return