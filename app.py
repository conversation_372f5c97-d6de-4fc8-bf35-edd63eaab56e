import sys
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from routes.user_router import router as user_router
from routes.sync_data_router import router as sync_data_router
from utils.logger import logger
from linebot.v3.exceptions import InvalidSignatureError
from utils.rich_menu import create_and_set_rich_menu, verify_rich_menu
from utils.validation import validate_durian_api_credentials, validate_line_credentials, validate_other_environment_variables
from handler.webhook_handler import handler

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(user_router)
app.include_router(sync_data_router)

@app.get("/")
async def health_check():
    return {"message": "OK"}

@app.post("/callback")
async def callback(request: Request):
    x_line_signature = request.headers.get("X-Line-Signature", "")
    body = await request.body()
    body_text = body.decode("utf-8")
    
    try:
        logger.info("Webhook LINE message body: %s", body_text)
        handler.handle(body_text, x_line_signature)
    except InvalidSignatureError:
        logger.error("Invalid signature")
        raise HTTPException(status_code=400, detail="Invalid signature")
    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
    
    return {"message": "OK"}

if __name__ == "__main__":
    try:
        logger.info("Validating LINE credentials...")
        validate_line_credentials()

        logger.info("Validating Durian API credentials...")
        validate_durian_api_credentials()

        logger.info("Validating other environment variables...")
        validate_other_environment_variables()

        logger.info("Creating and setting up rich menu...")
        menu_id = create_and_set_rich_menu()

        logger.info("Verifying rich menu setup...")
        if not verify_rich_menu(menu_id):
            logger.error("Rich menu verification failed")
            sys.exit(1)

        logger.info("Initializing database...")

        logger.info("Starting LINE bot server on port 81")
        uvicorn.run("app:app", host="0.0.0.0", port=81, reload=True)
    except Exception as e:
        logger.error("Failed to start server: %s", str(e), exc_info=True)
        sys.exit(1)
