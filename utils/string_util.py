def format_17_digit_string(s: str):
    """
    Converts a 17-digit string into the format: XX-XXXX-XX-XXX-XXXXXX
    
    Args:
        s (str): A string containing exactly 17 digits.
        
    Returns:
        str: Formatted string in the pattern XX-XXXX-XX-XXX-XXXXXX
    """
    return f"{s[0:2]}-{s[2:6]}-{s[6:8]}-{s[8:11]}-{s[11:17]}"

def format_20_digit_string(s: str):
    """
    Converts a 20-digit string into the format: XXXXXX-XXXX-XXXX-XXXXXX
    
    Args:
        s (str): A string containing exactly 20 digits.
        
    Returns:
        str: Formatted string in the pattern XXXXXX-XXXX-XXXX-XXXXXX
    """
    return f"{s[0:6]}-{s[6:10]}-{s[10:14]}-{s[14:20]}"

def format_plot_gap(language: str, digits: str):
    """Format plot GAP to correct GAP format."""
    if not digits:
        return None
    formatted_17_digit = format_17_digit_string(digits)
    if language == 'thailand':
        return f"กษ {formatted_17_digit}"
    return f"AC {formatted_17_digit}"

def format_plot_id(digits: str):
    """Format plot ID to correct plot ID format."""
    if not digits:
        return None
    return format_20_digit_string(digits)