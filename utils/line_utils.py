from datetime import datetime, timedelta, timezone
from linebot.v3.messaging import (
    MessageAction,
    DatetimePickerAction,
    TemplateMessage,
    ButtonsTemplate,
    CarouselTemplate,
    CarouselColumn,
    URIAction,
    QuickReply,
    QuickReplyItem
)
from linebot.v3.messaging import (
    TextMessage,
    MessageAction,
    TemplateMessage,
    ButtonsTemplate,
)
from utils.durian_info import DURIAN_INFO
from utils.flex_message import create_flex_message_ask_durian_photo, create_flex_message_ask_location, create_flex_message_ask_upload_single_photo
from utils.language_mapping import get_displayed_texts
from utils.logger import logger
from utils.time_util import format_date

def create_date_picker_template(language: str, title, action_data: str, allow_skip: bool = False, initial_date: str | None = None):
    """Create a date picker template showing Thai dates."""
    return TemplateMessage(
        alt_text="Date picker",
        template=ButtonsTemplate(
            text=title,
            actions=[
                DatetimePickerAction(
                    label="📅 Select date" if language == 'english' else "📅 เลือกวันที่",
                    data=action_data,
                    mode="date",
                    initial=initial_date,
                    # max=thai_now.strftime('%Y-%m-%d')
                )] + ([MessageAction(
                        label="Skip" if language == 'english' else "ข้าม",
                        text="Skip" if language == 'english' else "ข้าม"
                    )] if allow_skip else [])
        )
    )

def create_datetime_picker_template(language: str, title, action_data: str, allow_skip: bool = False, initial_date: str | None = None):
    """Create a datetime picker template showing Thai dates."""
    tz_offset = timedelta(hours=7)
    now = datetime.now(tz=timezone(tz_offset))
    # thai_now = gregorian_to_thai(now)
    return TemplateMessage(
        alt_text="Datetime picker",
        template=ButtonsTemplate(
            text=title,
            actions=[
                DatetimePickerAction(
                    label="📅 Select datetime" if language == 'english' else "📅 เลือกวันที่เวลา",
                    data=action_data,
                    mode="datetime",
                    initial=initial_date,
                    # max=now.strftime('%Y-%m-%dT%H:%M')
                )] + ([MessageAction(
                        label="Skip" if language == 'english' else "ข้าม",
                        text="Skip" if language == 'english' else "ข้าม"
                    )] if allow_skip else [])
        )
    )

def create_selection_buttons(title, items):
    """
    Create a template with selection buttons.
    Return a FlexMessage.
    """
    actions = []
    for item in items:
        if type(item) == str:
            actions.append({
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": item,
                        "align": "center",
                        "color": "#44679b"
                    }
                ],
                "paddingAll": "lg",
                "action": {
                    "type": "message",
                    "label": "action",
                    "text": item
                }
            })
        elif type(item) == dict:
            actions.append({
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": item.get('label', ' '),
                        "align": "center",
                        "color": item.get('color', "#44679b")
                    }
                ],
                "paddingAll": "lg",
                "action": {
                    "type": "message",
                    "label": "action",
                    "text": item.get('text', ' ')
                }
            })
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": title,
                    "wrap": True
                },
                {
                    "type": "separator",
                    "margin": "xl"
                }
            ] + actions
        }
    }

def create_selection_postback_buttons(title: str, items: list[dict]):
    """
    Create a template with selection buttons with action type is postback.
    Return a FlexMessage.
    """
    actions = []
    for item in items:
        if type(item) == str:
            # Ignore string items
            continue
        elif type(item) == dict:
            actions.append({
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": item['label'],
                        "align": "center",
                        "color": item.get('color', "#44679b")
                    }
                ],
                "paddingAll": "lg",
                "action": {
                    "type": "postback",
                    "label": item.get('text', ' '),
                    "data": item.get('data', ' '),
                    "displayText": item.get('text', ' ')
                }
            })
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": title,
                    "wrap": True
                },
                {
                    "type": "separator",
                    "margin": "xl"
                }
            ] + actions
        }
    }

def create_line_button_message(displayed_message, btn_title, uri):
    """Create a line button message with a link."""
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "spacing": "md",
            "contents": [
                {
                    "type": "text",
                    "text": displayed_message,
                    "wrap": True
                },
                {
                    "type": "button",
                    "style": "link",
                    "action": {
                        "type": "uri",
                        "label": btn_title,
                        "uri": uri
                    },
                    "height": "sm"
                }
            ]
        }
    }

def create_line_multiple_buttons_message(displayed_message: str, buttons: list[dict]):
    """Create a line button message with multiple buttons."""
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "spacing": "md",
            "contents": [
                {
                    "type": "text",
                    "text": displayed_message,
                    "wrap": True
                },
                *[{
                    "type": "button",
                    "style": "link",
                    "action": {
                        "type": "uri",
                        "label": btn.get('tittle', ' '),
                        "uri": btn.get('uri', 'https://example.com')
                    },
                    "height": "sm"
                } for btn in buttons]
            ]
        }
    }

def create_cutter_selection_button_list(items: list[dict]):
    """
    Create a carousel template with selection buttons for cutters.
    Each bubble can contain up to 10 buttons.
    Return a FlexMessage with carousel layout.
    """
    # Maximum number of buttons per bubble
    MAX_BUTTONS_PER_BUBBLE = 10
    
    # Calculate how many bubbles we need
    num_bubbles = (len(items) + MAX_BUTTONS_PER_BUBBLE - 1) // MAX_BUTTONS_PER_BUBBLE
    
    bubbles = []
    for bubble_index in range(num_bubbles):
        # Get items for this bubble
        start_idx = bubble_index * MAX_BUTTONS_PER_BUBBLE
        end_idx = min(start_idx + MAX_BUTTONS_PER_BUBBLE, len(items))
        bubble_items = items[start_idx:end_idx]

        # Create button actions for this bubble
        button_actions = []
        for item in bubble_items:
            button_actions.append({
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": item.get("label", " "),
                        "align": "center",
                        "color": item.get("color", "#44679b")
                    }
                ],
                "paddingAll": "lg",
                "action": {
                    "type": "postback",
                    "label": "action",
                    "data": item.get("data", " "),
                    "displayText": item.get("text", ' ')
                }
            })

        # Create bubble
        bubble = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": button_actions,
                "spacing": "md"
            }
        }
        
        bubbles.append(bubble)
    
    # Create carousel
    return {
        "type": "carousel",
        "contents": bubbles
    }

def create_carousel_selection_buttons(title: str, items: list[dict]):
    """
    Create a carousel template with selection buttons.
    Each bubble can contain up to 10 buttons.
    Return a FlexMessage with carousel layout.
    """
    # Maximum number of buttons per bubble
    MAX_BUTTONS_PER_BUBBLE = 10
    
    # Calculate how many bubbles we need
    num_bubbles = (len(items) + MAX_BUTTONS_PER_BUBBLE - 1) // MAX_BUTTONS_PER_BUBBLE

    bubbles = []
    for bubble_index in range(num_bubbles):
        # Get items for this bubble
        start_idx = bubble_index * MAX_BUTTONS_PER_BUBBLE
        end_idx = min(start_idx + MAX_BUTTONS_PER_BUBBLE, len(items))
        bubble_items = items[start_idx:end_idx]

        # Create button actions for this bubble
        if bubble_index == 0:
            button_actions = [
                {
                    "type": "text",
                    "text": title,
                    "wrap": True
                },
                {
                    "type": "separator",
                    "margin": "xl"
                }
            ]
        else:
            button_actions = []

        for item in bubble_items:
            button_actions.append({
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": item.get("label", " "),
                        "align": "center",
                        "color": item.get("color", "#44679b")
                    }
                ],
                "paddingAll": "lg",
                "action": {
                    "type": "message",
                    "label": "action",
                    "text": item.get("text", ' ')
                }
            })

        # Create bubble
        bubble = {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": button_actions,
                "spacing": "md"
            }
        }
        
        bubbles.append(bubble)
    
    # Create carousel
    return {
        "type": "carousel",
        "contents": bubbles
    }

def create_text_message_with_skip_button(title: str, language: str):
    """Create a text message with a skip button."""
    return TemplateMessage(
        alt_text=title,
        template=ButtonsTemplate(
            text=title,
            actions=[
                MessageAction(
                    label="Skip" if language == 'english' else "ข้าม",
                    text="Skip" if language == 'english' else "ข้าม"
                )
            ]
        )
    )

def create_multiple_type_selection_buttons(title: str, items: list[dict]):
    """
    Create a template with selection buttons with different action types.
    Return a FlexMessage.
    """
    actions = []
    for item in items:
        if type(item) != dict:
            # Ignore not dict items
            continue
        match item['type']:
            case 'postback':
                actions.append({
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": item.get('label', ' '),
                            "align": "center",
                            "color": item.get('color', "#44679b")
                        }
                    ],
                    "paddingAll": "lg",
                    "action": {
                        "type": "postback",
                        "label": "postback",
                        "data": item.get('data', ' '),
                        "displayText": item.get('text', ' ')
                    }
                })
            case 'uri':
                actions.append({
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": item.get('label', ' '),
                            "align": "center",
                            "color": item.get('color', "#44679b")
                        }
                    ],
                    "paddingAll": "lg",
                    "action": {
                        "type": "uri",
                        "label": "uri",
                        "uri": item.get('uri', 'https://example.com')
                    }
                })
            case 'message':
                actions.append({
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": item.get('label', ' '),
                            "align": "center",
                            "color": item.get('color', "#44679b")
                        }
                    ],
                    "paddingAll": "lg",
                    "action": {
                        "type": "message",
                        "label": "message",
                        "text": item.get('text', ' ')
                    }
                })
            case _:
                pass

    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": title,
                    "wrap": True
                },
                {
                    "type": "separator",
                    "margin": "xl"
                }
            ] + actions
        }
    }
        

def create_harvest_question_response(language: str, conversation_state: str, extra_context: dict = {}):
    """Create a harvest question response."""
    state_to_key_mapping = {
        "AWAITING_HARVEST_LOT_NAME": "Ask harvest lot name",
        "AWAITING_PLOT_SELECTION": "Ask plot selection",
        "AWAITING_ADDITIONAL_PLOTS": "Ask additional plots",
        "AWAITING_VARIETY_SELECTION": "Ask variety selection",
        "AWAITING_ADD_VARIETY_NAME": "Ask variety name",
        "AWAITING_ADDITIONAL_VARIETIES": "Ask additional varieties",
        "WARNING_BLOOMING_DATE": "Warning blooming date",
        "AWAITING_BLOOMING_DATE_PER_VARIETY": "Ask blooming date per variety",
        "AWAITING_GRADE_WEIGHT_PER_VARIETY": "Ask grade weight per variety",
        "AWAITING_WEIGHT_PER_VARIETY": "Ask weight per variety",
        "AWAITING_ADDITIONAL_GRADES": "Ask additional grades",
        "AWAITING_BLOOMING_DATE": "Ask blooming date",
        "AWAITING_HARVEST_DATE": "Ask harvest date",
        "AWAITING_DURIAN_PHOTO": "Ask durian photo",
        "AWAITING_PACKING_HOUSE": "Ask packing house",
        "AWAITING_CUTTER_PHOTO": "Ask cutter photo",
        "AWAITING_FINAL_CONFIRMATION": "Ask final confirmation",
        "AWAITING_CUTTER_SELECTION": "Ask cutter selection",
        "AWAITING_CUTTER_NAME": "Ask cutter name",
        "AWAITING_CUTTER_REGISTRATION": "Ask cutter registration",
        "AWAITING_CUTTER_REGISTRATION_NUMBER": "Ask cutter registration number",
        "AWAITING_SEND_OR_DRAFT": "Ask send or draft",
        "AWAITING_VEHICLE_PHOTO": "Ask vehicle photo",
        "AWAITING_VEHICLE_REGISTRATION_NUMBER": "Ask vehicle registration number",
        "AWAITING_VEHICLE_REGISTRATION_PROVINCE": "Ask vehicle registration province",
        "AWAITING_FARM_SELECTION": "Ask farm selection",
        "AWAITING_PLOT_CERTIFICATE_IMAGE": "Ask plot certificate image",
        "AWAITING_ADD_FARM_NAME": "Ask adding farm name",
        "AWAITING_ADD_FARM_LOCATION": "Ask adding farm location",
        "AWAITING_ADD_PLOT_NAME": "Ask adding plot name",
        "AWAITING_ADD_PLOT_GAP": "Ask adding plot gap",
        "AWAITING_ADD_PLOT_AREA": "Ask adding plot area",
        "AWAITING_ADD_PLOT_ID": "Ask adding plot id",
        "AWAITING_ADD_PLOT_CONFIRMATION": "Ask adding plot confirmation",
        "SAVE_AS_DRAFT": "Prompt save as draft",
        "CONFIRM_AND_SEND": "Prompt confirm and send",
        "EDIT_AND_SEND": "Prompt edit and send",
        "PLOT_NOT_EXIST": "Prompt plot not exist"
    }

    key = state_to_key_mapping.get(conversation_state, "Invalid input")
    displayed_texts = get_displayed_texts(key, language)

    if conversation_state == "INIT":
        user_role = extra_context.get('user_role', 'farmer')
        if user_role == 'farmer':
            displayed_text = get_displayed_texts("Instruction for farmer", language)[0]
        else:
            displayed_text = get_displayed_texts("Instruction for cutter", language)[0]
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_text)]
        }

    elif conversation_state == "AWAITING_HARVEST_LOT_NAME":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_BLOOMING_DATE":
        return {
            "type": "reply",
            "message": [create_date_picker_template(language, displayed_texts[0], "action=blooming_date")]
        }

    elif conversation_state == "AWAITING_HARVEST_DATE":
        return {
            "type": "reply",
            "message": [create_datetime_picker_template(language, displayed_texts[0], "action=harvest_date")]
        }

    elif conversation_state == "AWAITING_DURIAN_PHOTO":
        return {
            "type": "flex",
            "message": [create_flex_message_ask_durian_photo(language, extra_context.get('allow_skip', False))]
        }

    elif conversation_state == "AWAITING_CUTTER_SELECTION":
        cutters: list[dict] = extra_context.get('cutters', [])
        cutter_items = []
        for cutter in cutters:
            cutter_items.append({
                "label": f"{cutter.get('first_name', '')} {cutter.get('last_name', '')}",
                "text": f"{cutter.get('first_name', '')} {cutter.get('last_name', '')}",
                "data": f"action=select_cutter&cutter_id={cutter.get('id')}"
            })
        cutter_items.append({
            "label": "+ Add new cutter" if language == 'english' else "+ เพิ่มชื่อใหม่",
            "text": "+ Add new cutter" if language == 'english' else "+ เพิ่มชื่อใหม่",
            "data": f"action=add_new_cutter"
        })
        return [
            {
                "type": "reply",
                "message": [TextMessage(text=displayed_texts[0])]
            },
            {
                "type": "flex",
                "message": [create_cutter_selection_button_list(cutter_items)]
            }
        ]

    elif conversation_state == "AWAITING_CUTTER_NAME":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_CUTTER_REGISTRATION":
        actions = [
            "Already registered" if language == 'english' else "ขึ้นทะเบียนแล้ว",
            "I do not know" if language == 'english' else "ไม่ทราบ"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "AWAITING_CUTTER_REGISTRATION_NUMBER":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_PACKING_HOUSE":
        packing_houses = extra_context.get('packing_houses', [])
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], packing_houses)]
        }

    elif conversation_state == "AWAITING_CUTTER_PHOTO":
        return {
            "type": "flex",
            "message": [create_flex_message_ask_upload_single_photo(displayed_texts[0], language)]
        }

    elif conversation_state == "AWAITING_PLOT_SELECTION":
        plot_labels: list = extra_context.get('plots', [])
        allow_add_new_plot = extra_context.get('allow_add_new_plot', False)
        if not plot_labels:
            displayed_text = get_displayed_texts("Ask to add a new plot", language)[0]
        # elif is_add_more_plot:
        #     displayed_text = "กรุณาเลือกแปลงเพิ่มเติม" if language == 'thailand' else "Please select additional conversions."
        else:
            displayed_text = "กรุณาเลือกแปลง" if language == 'thailand' else "Please select a plot."
        if allow_add_new_plot:
            plot_labels.append("+ เพิ่มแปลง" if language == 'thailand' else "+ Add more plot")
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_text, plot_labels)]
        }

    elif conversation_state == "AWAITING_PLOT_CERTIFICATE_IMAGE":
        plot_name = extra_context.get('plot_name', '')
        prompt = displayed_texts[0].format(plot_name=plot_name)
        return {
            "type": "flex",
            "message": [create_flex_message_ask_upload_single_photo(prompt, language)]
        }

    elif conversation_state == "AWAITING_ADDITIONAL_PLOTS":
        actions = [
            "Yes, add more plots" if language == 'english' else "ใช่เพิ่มรายละเอียดแปลง",
            "No" if language == 'english' else "ไม่มี"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "AWAITING_VARIETY_SELECTION":
        varieties = extra_context.get('varieties', [])
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], varieties)]
        }

    elif conversation_state == "AWAITING_ADD_VARIETY_NAME":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_ADDITIONAL_VARIETIES":
        actions = [
            "Yes, add more varieties" if language == 'english' else "ใช่เพิ่มรายละเอียดทุเรียน",
            "No" if language == 'english' else "ไม่มี"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "WARNING_BLOOMING_DATE":
        variety_name = extra_context.get('variety_name', '')
        flower_blooming_duration = extra_context.get('flower_blooming_duration', '')
        prompt = displayed_texts[0].format(variety_name=variety_name, variety_allow_day=flower_blooming_duration)
        initial_date = extra_context.get('initial_date', '')
        return {
            "type": "reply",
            "message": [create_date_picker_template(language, prompt, "action=blooming_date_per_variety", allow_skip=True, initial_date=initial_date)]
        }

    elif conversation_state == "AWAITING_BLOOMING_DATE_PER_VARIETY":
        current_variety = extra_context.get('current_variety', '')
        initial_date = extra_context.get('initial_date', '')
        prompt = displayed_texts[0].format(variety=current_variety)
        return {
            "type": "reply",
            "message": [create_date_picker_template(language, prompt, "action=blooming_date_per_variety", initial_date=initial_date)]
        }

    elif conversation_state == "AWAITING_GRADE_WEIGHT_PER_VARIETY":
        current_variety = extra_context.get('current_variety', '')
        grades = extra_context.get('grades', [])
        prompt = displayed_texts[0].format(variety=current_variety)

        if grades:
            return {
                "type": "flex",
                "message": [create_selection_buttons(prompt, grades)]
            }
        return {
            "type": "reply",
            "message": [TextMessage(text=prompt)]
        }

    elif conversation_state == "AWAITING_WEIGHT_PER_VARIETY":
        grade = extra_context.get('grade', '')
        prompt = displayed_texts[0].format(grade=grade)
        return {
            "type": "reply",
            "message": [TextMessage(text=prompt)]
        }

    elif conversation_state == "AWAITING_ADDITIONAL_GRADES":
        actions = [
            "ใช่เพิ่มรายละเอียดเกรด" if language == "thailand" else "Yes, add more grades",
            "ไม่มี" if language == "thailand" else "No"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "AWAITING_GRADE_WEIGHT_CONFIRMATION":
        current_variety = extra_context.get('current_variety', '')
        variety_name = extra_context.get('variety_name', '')
        grades_weights = extra_context.get('grades_weights', {})
        total_weight = extra_context.get('total_weight', 0)

        # Generate acknowledgment message
        if language == 'english':
            ack_message = f"Summary of durian grades and weights as detailed below, yes or no?\n\n{variety_name}\n"
            for grade, weight in grades_weights.items():
                grade_name = DURIAN_INFO.mapping_grade_value_to_text(current_variety, grade, language)
                ack_message += f"- Grade {grade_name} {weight} kg.\n"
            ack_message += f"\nTotal {total_weight} kg."
        else:  # Thai language
            ack_message = f"สรุปเกรดและน้ำหนักทุเรียน ตามรายละเอียดด้านล่างนี้ ใช่หรือไม่\n\n{variety_name}\n"
            for grade, weight in grades_weights.items():
                grade_name = DURIAN_INFO.mapping_grade_value_to_text(current_variety, grade, language)
                ack_message += f"- เกรด {grade_name} {weight} กก.\n"
            ack_message += f"\nรวมทั้งหมด {total_weight} กก."

        actions = [
            "Confirm" if language == 'english' else "ยืนยัน",
            "Edit" if language == 'english' else "แก้ไข"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(ack_message, actions)]
        }

    elif conversation_state == "AWAITING_VEHICLE_PHOTO":
        return {
            "type": "flex",
            "message": [create_flex_message_ask_upload_single_photo(
                display_message=displayed_texts[0],
                language=language
            )]
        }

    elif conversation_state == "AWAITING_VEHICLE_REGISTRATION_NUMBER":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_VEHICLE_REGISTRATION_PROVINCE":
        actions = []
        for province in DURIAN_INFO.province[language.lower()]:
            actions.append({
                "label": province,
                "text": province,
            })
        return {
            "type": "flex",
            "message": [create_carousel_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "AWAITING_SEND_OR_DRAFT":
        send_packing_house = get_displayed_texts("Send packing house", language)[0]
        save_as_draft = get_displayed_texts("Draft action", language)[0]
        actions = [
            { "label": send_packing_house, "text": send_packing_house, "color": "#0060AE"},
            { "label": save_as_draft, "text": save_as_draft, "color": "#6D6D6D"},
        ]

        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "SAVE_AS_DRAFT":
        view_detail_link = extra_context.get('view_detail_link', '')
        btn_title = "View detail" if language == 'english' else "ดูรายละเอียด"
        return {
            "type": "flex",
            "message": [create_line_button_message(displayed_texts[0], btn_title, view_detail_link)]
        }

    elif conversation_state == "AWAITING_FINAL_CONFIRMATION":
        event_id = extra_context.get('event_id', '')
        view_detail_link = extra_context.get('view_detail_link', '')
        confirm_action = get_displayed_texts("Confirm action", language)[0]
        edit_action = get_displayed_texts("Edit action", language)[0]
        draft_action = get_displayed_texts("Draft action", language)[0]
        actions = [
            { "label": confirm_action, "text": confirm_action, "color": "#0060AE", "data": f"action=confirm_send&event_id={event_id}", "type": "postback" },
            { "label": edit_action, "text": edit_action, "color": "#FF0000", "uri": view_detail_link, "type": "uri" },
            { "label": draft_action, "text": draft_action, "color": "#6D6D6D", "data": f"action=save_as_draft&event_id={event_id}", "type": "postback" },
        ]
        return {
            "type": "flex",
            "message": [create_multiple_type_selection_buttons(displayed_texts[0], actions)]
        }

    elif conversation_state == "AWAITING_FARM_SELECTION":
        farms = extra_context.get('farms', [])
        # farms.append("+ เพิ่มสวน" if language == 'thailand' else "+ Add more farms")
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], farms)]
        }

    elif conversation_state == "AWAITING_ADD_FARM_NAME":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_ADD_FARM_LOCATION":
        return {
            "type": "flex",
            "message": [
                create_flex_message_ask_location(
                    display_message=displayed_texts[0],
                    display_button="เลือกตำแหน่งที่อยู่สวน" if language == 'thailand' else "Select farm location",
                    language=language
                )
            ]
        }

    elif conversation_state == "AWAITING_ADD_PLOT_NAME":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_ADD_PLOT_GAP":
        return {
            "type": "reply",
            "message": [create_text_message_with_skip_button(displayed_texts[0], language)]
        }

    elif conversation_state == "AWAITING_ADD_PLOT_AREA":
        return {
            "type": "reply",
            "message": [TextMessage(text=displayed_texts[0])]
        }

    elif conversation_state == "AWAITING_ADD_PLOT_ID":
        return {
            "type": "reply",
            "message": [create_text_message_with_skip_button(displayed_texts[0], language)]
        }

    elif conversation_state == "AWAITING_ADD_PLOT_CONFIRMATION":
        plot_name = extra_context.get('plot_name', '--')
        gap = extra_context.get('gap') or '--'
        area = extra_context.get('area', 0)
        plot_id = extra_context.get('plot_id') or '--'
        displayed_message = displayed_texts[0].format(plot_name=plot_name, gap=gap, area=area, plot_id=plot_id)
        actions = [
            "Confirm" if language == 'english' else "ถูกต้อง",
            "Edit" if language == 'english' else "แก้ไข"
        ]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_message, actions)]
        }

    elif conversation_state == "CONFIRM_AND_SEND":
        packing_house = extra_context.get('packing_house', '')
        view_detail_link = extra_context.get('view_detail_link', '')
        btn_title = "View detail" if language == 'english' else "ดูรายละเอียด"
        displayed_message = displayed_texts[0].format(packing_house=packing_house)
        return {
            "type": "flex",
            "message": [create_line_button_message(displayed_message, btn_title, view_detail_link)]
        }

    elif conversation_state == "EDIT_AND_SEND":
        harvest_lot_name = extra_context.get('harvest_lot_name', '')
        view_detail_link = extra_context.get('view_detail_link', '')
        btn_title = "Edit" if language == 'english' else "แก้ไข"
        displayed_message = displayed_texts[0].format(harvest_lot_name=harvest_lot_name)
        return {
            "type": "flex",
            "message": [create_line_button_message(displayed_message, btn_title, view_detail_link)]
        }

    elif conversation_state == "PLOT_NOT_EXIST":
        actions = ["Add new record" if language == 'english' else "เพิ่มการเก็บเกี่ยวใหม่"]
        return {
            "type": "flex",
            "message": [create_selection_buttons(displayed_texts[0], actions)]
        }

    else:
        logger.info(f"Missing conversation state when creating harvest question response: {conversation_state}")
