from datetime import datetime
from utils.config import LOGIN_LOGOUT_LOGO
from utils.language_mapping import get_displayed_texts
from utils.string_util import format_plot_gap
from utils.time_util import format_date


def create_durian_info_flex_message(language: str, harvest_lot_name: str, batchlot: str, farm_name: str, farm_address: str, plots: list[dict[str, str | float]], variety_details: dict[str, dict[str, str | dict[str, float]]], total_weight: str, image_links: list[str], harvest_date: datetime | str, packing_house: str, cutter_name: str, cutter_avatar_link: str, cutter_registration: bool, doa_number: str | None, vehicle_image_link: str, vehicle_registration_number: str, vehicle_registration_province: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                # Batch name
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": f"{harvest_lot_name}",
                            "color": "#ffffff",
                            "weight": "bold",
                            "size": "16px"
                        }
                    ],
                    "spacing": "4px"
                },
                # Harvest Information
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Batch/Lot" if language == 'english' else "เลขที่ล็อต",
                                    "flex": 0,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": f"{batchlot}",
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end"
                                }
                            ],
                            "spacing": "16px"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Farm name" if language == 'english' else "สวน",
                                    "flex": 2,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": f"{farm_name}",
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end",
                                    "flex": 3,
                                    "wrap": True
                                }
                            ]
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Address" if language == 'english' else "ที่ตั้ง",
                                    "flex": 1,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": f"{farm_address}",
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end",
                                    "wrap": True,
                                    "flex": 3
                                }
                            ]
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Total weight" if language == 'english' else "น้ำหนักรวม",
                                    "flex": 0,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": f"{total_weight}",
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end"
                                }
                            ]
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Durian cutting day" if language == 'english' else "วันที่เก็บเกี่ยว",
                                    "flex": 0,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": format_date(harvest_date),
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end"
                                }
                            ]
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Packing house" if language == 'english' else "ขายให้โรงคัดบรรจุ",
                                    "flex": 2,
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "start"
                                },
                                {
                                    "type": "text",
                                    "text": f"{packing_house}",
                                    "size": "12px",
                                    "color": "#ffffff",
                                    "align": "end",
                                    "wrap": True,
                                    "flex": 3
                                }
                            ]
                        }
                    ],
                    "spacing": "12px"
                },
                # Plot information
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": "Plot" if language == 'english' else "แปลงที่เก็บเกี่ยว",
                            "color": "#FFFFFF",
                            "size": "14px"
                        },
                        *[item for plot in plots for item in [{
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "box",
                                    "layout": "vertical",
                                    "contents": [
                                        {
                                            "type": "text",
                                            "text": f"{plot.get('name') or '--'}",
                                            "size": "14px",
                                            "color": "#FFFFFF"
                                        },
                                        {
                                            "type": "text",
                                            "text": f"ID - {plot.get('plot_id') or '--'}",
                                            "size": "12px",
                                            "color": "#E5E5E5"
                                        },
                                        {
                                            "type": "text",
                                            "text": f"GAP - {format_plot_gap(language, plot.get('gap')) or '--'}",
                                            "size": "12px",
                                            "color": "#E5E5E5"
                                        },
                                        {
                                            "type": "text",
                                            "text": f"Area - {plot.get('area') or '--'} Rai" if language == 'english' else f"พื้นที่ - {plot.get('area') or '--'} ไร่",
                                            "size": "12px",
                                            "color": "#E5E5E5"
                                        }
                                    ],
                                    "spacing": "4px",
                                    "flex": 4
                                },
                                {
                                    "type": "image",
                                    "url": f"{plot.get('plot_certificate_link') or 'https://developers-resource.landpress.line.me/fx/img/01_1_cafe.png'}",
                                    "size": "sm",
                                    "align": "end",
                                    "aspectRatio": "3:4",
                                    "flex": 1,
                                    "action": {
                                        "type": "uri",
                                        "uri": f"{plot.get('plot_certificate_link') or 'https://developers-resource.landpress.line.me/fx/img/01_1_cafe.png'}"
                                    }
                                }
                            ],
                            "alignItems": "center",
                            "justifyContent": "space-between",
                            "paddingStart": "12px",
                            "spacing": "4px"
                        },
                        {
                            "type": "separator",
                            "color": "#D1D5DB"
                        }]][:-1]
                    ],
                    "spacing": "4px"
                },
                # Variety information
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": "Durian varieties, grades and weight (kg)" if language == 'english' else "สายพันธุ์ทุเรียน, เกรดและน้ำหนัก (กก.)",
                            "flex": 0,
                            "size": "14px",
                            "color": "#ffffff",
                            "align": "start"
                        },
                        *[{
                            "type": "box",
                            "layout": "vertical",
                            "contents": [
                                {
                                    "type": "box",
                                    "layout": "vertical",
                                    "contents": [
                                    {
                                        "type": "text",
                                        "text": variety,
                                        "size": "12px"
                                    },
                                    {
                                        "type": "text",
                                        "text": f"Blooming on {format_date(details['blooming_date'])}" if language == 'english'
                                                    else f"วันที่ดอกทุเรียนบาน {format_date(details['blooming_date'])}",
                                        "size": "10px",
                                        "color": "#9CA3AF"
                                    }
                                    ]
                                },
                                *[{
                                    "type": "box",
                                    "layout": "horizontal",
                                    "contents": [
                                        {
                                            "type": "box",
                                            "layout": "vertical",
                                            "backgroundColor": "#DBEAFE",
                                            "contents": [
                                                {
                                                    "type": "text",
                                                    "text": f"{grade} - {weight}",
                                                    "size": "10px",
                                                    "color": "#050505"
                                                }
                                            ],
                                            "cornerRadius": "24px",
                                            "paddingTop": "10px",
                                            "paddingBottom": "10px",
                                            "paddingStart": "8px",
                                            "flex": 0,
                                            "paddingEnd": "8px"
                                        }
                                        for grade, weight in chunk
                                    ],
                                    "spacing": "sm",
                                    "flex": 0
                                }
                                for chunk in [list(details.get('grades_weights').items())[i:i+2] for i in range(0, len(details.get('grades_weights') or {}), 2)]]
                            ],
                            "spacing": "8px",
                            "backgroundColor": "#EFF6FF",
                            "paddingAll": "8px",
                            "borderWidth": "1px",
                            "cornerRadius": "4px",
                            "borderColor": "#E5E7EB"
                        } for variety, details in variety_details.items()]
                    ],
                    "spacing": "8px"
                },
                # Cutter information
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "spacing": "sm",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Cutter" if language == 'english' else "นักคัดนักตัด",
                                    "wrap": True,
                                    "color": "#8E8E93",
                                    "size": "12px",
                                    "gravity": "center"
                                },
                                {
                                    "type": "box",
                                    "layout": "horizontal",
                                    "contents": [
                                        {
                                            "type": "box",
                                            "layout": "vertical",
                                            "contents": [
                                                {
                                                    "type": "image",
                                                    "url": f"{cutter_avatar_link}",
                                                    "size": "xs",
                                                    "align": "center",
                                                    "aspectMode": "cover"
                                                }
                                            ],
                                            "cornerRadius": "xxl",
                                            "alignItems": "center",
                                            "justifyContent": "center",
                                            "width": "21.25px",
                                            "height": "21.25px"
                                        },
                                        {
                                            "type": "text",
                                            "text": f"{cutter_name}",
                                            "wrap": True,
                                            "align": "end",
                                            "color": "#050505",
                                            "size": "12px",
                                            "flex": 0
                                        }
                                    ],
                                    "alignItems": "center",
                                    "spacing": "sm",
                                    "justifyContent": "flex-end"
                                }
                            ],
                            "justifyContent": "space-between",
                            "alignItems": "center",
                            "height": "21.25px"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "spacing": "12px",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Certified cutter" if language == 'english' else "นักตัดที่ได้รับการรับรอง",
                                    "wrap": True,
                                    "color": "#8E8E93",
                                    "size": "12px"
                                },
                                {
                                    "type": "text",
                                    "text": "Yes" if language == 'english' else "ใช่"
                                        if cutter_registration else "No" if language == 'english' else "ไม่มี",
                                    "wrap": True,
                                    "align": "end",
                                    "color": "#000000",
                                    "size": "12px",
                                    "flex": 0
                                }
                            ],
                            "justifyContent": "space-between"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                            {
                                "type": "text",
                                "text": "Car registeration\nnumber of cutter" if language == 'english' else "ทะเบียนรถนักคัดนักตัด",
                                "size": "12px",
                                "color": "#8E8E93",
                                "wrap": True
                            },
                            {
                                "type": "image",
                                "url": vehicle_image_link,
                                "flex": 0,
                                "action": {
                                "type": "uri",
                                "uri": vehicle_image_link
                                }
                            }
                            ],
                            "alignItems": "center",
                            "spacing": "16px",
                            "flex": 0,
                            "justifyContent": "space-between"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "spacing": "12px",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Vehicle registration number" if language == 'english' else "หมายเลขทะเบียนรถ",
                                    "wrap": True,
                                    "color": "#8E8E93",
                                    "size": "12px"
                                },
                                {
                                    "type": "text",
                                    "text": f"{vehicle_registration_number}",
                                    "wrap": True,
                                    "align": "end",
                                    "color": "#000000",
                                    "size": "12px"
                                }
                            ],
                            "justifyContent": "space-between"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "spacing": "12px",
                            "contents": [
                                {
                                    "type": "text",
                                    "text": "Registered province of vehicle" if language == 'english' else "จังหวัดหมายเลขทะเบียนรถ",
                                    "wrap": True,
                                    "color": "#8E8E93",
                                    "size": "12px"
                                },
                                {
                                    "type": "text",
                                    "text": f"{vehicle_registration_province}",
                                    "wrap": True,
                                    "align": "end",
                                    "color": "#000000",
                                    "size": "12px"
                                }
                            ],
                            "justifyContent": "space-between"
                        }
                    ],
                    "backgroundColor": "#EFF6FF",
                    "paddingAll": "xl",
                    "cornerRadius": "md",
                    "spacing": "12px"
                },
                {
                    "type": "separator"
                },
                # Harvest images
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "image",
                                    "url": f"{image_link}",
                                    "size": "full",
                                    "aspectMode": "cover",
                                    "action": {
                                        "type": "uri",
                                        "uri": f"{image_link}"
                                    }
                                }
                                for image_link in pair_of_images
                            ],
                            "spacing": "sm"
                        } for pair_of_images in [image_links[i:i+2] for i in range(0, len(image_links), 2)]
                    ],
                    "spacing": "sm"
                }
            ],
            "background": {
                "type": "linearGradient",
                "angle": "0deg",
                "startColor": "#A4B9DD",
                "endColor": "#2266D9",
                "centerColor": "#5888DB"
            },
            "paddingTop": "12px",
            "paddingBottom": "24px",
            "paddingStart": "16px",
            "paddingEnd": "16px",
            "spacing": "8px",
            "cornerRadius": "12px"
        }
    }


def create_flex_message_confirm_harvest(language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": "Please confirm the accuracy of the information" if language == 'english' else "โปรดยืนยันข้อมูลความถูกต้อง",
                    "wrap": True
                },
                {
                    "type": "separator",
                    "margin": "lg"
                },
                {
                    "type": "button",
                    "action": {
                        "type": "postback",
                        "label": "Confirm information" if language == 'english' else "ยืนยันข้อมูล",
                        "data": "action=confirm",
                        "displayText": "Confirm information" if language == 'english' else "ยืนยันข้อมูล"
                    },
                    "color": "#0060AE"
                },
                {
                    "type": "button",
                    "action": {
                        "type": "postback",
                        "label": "Cancel" if language == 'english' else "ยกเลิก",
                        "data": "action=cancel",
                        "displayText": "Cancel" if language == 'english' else "ยกเลิก"
                    },
                    "color": "#FF0000"
                }
            ],
            "position": "relative"
        }
    }


def create_cutter_info_flex_message(language: str, cutter_name: str, avatar_link: str, cutter_registration: bool, doa_number: str | None, vehicle_image_link: str, vehicle_registration_number: str, vehicle_registration_province: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": "ข้อมูลนักคัดนักตัด" if language == 'thailand' else "Cutter information",
                    "weight": "bold",
                    "size": "xl"
                },
                {
                    "type": "box",
                    "layout": "horizontal",
                    "spacing": "sm",
                    "contents": [
                        {
                            "type": "text",
                            "text": "ชื่อนักคัดนักตัด" if language == 'thailand' else "Name of Cutter",
                            "wrap": True,
                            "color": "#8E8E93",
                            "size": "xs",
                            "gravity": "center"
                        },
                        {
                            "type": "box",
                            "layout": "horizontal",
                            "contents": [
                                {
                                    "type": "box",
                                    "layout": "vertical",
                                    "contents": [
                                        {
                                            "type": "image",
                                            "url": f"{avatar_link}",
                                            "size": "xs",
                                            "align": "center",
                                            "aspectMode": "cover"
                                        }
                                    ],
                                    "cornerRadius": "xxl",
                                    "alignItems": "center",
                                    "justifyContent": "center",
                                    "width": "21.25px",
                                    "height": "21.25px"
                                },
                                {
                                    "type": "text",
                                    "text": f"{cutter_name}",
                                    "wrap": True,
                                    "align": "end",
                                    "color": "#050505",
                                    "size": "xs",
                                    "flex": 0
                                }
                            ],
                            "alignItems": "center",
                            "spacing": "sm",
                            "justifyContent": "flex-end"
                        }
                    ],
                    "justifyContent": "space-between",
                    "alignItems": "center",
                    "height": "21.25px"
                },
                {
                    "type": "box",
                    "layout": "baseline",
                    "spacing": "sm",
                    "contents": [
                        {
                            "type": "text",
                            "text": "นักตัดที่ได้รับการรับรอง" if language == 'thailand' else "Certified cutter",
                            "wrap": True,
                            "color": "#8E8E93",
                            "size": "xs"
                        },
                        {
                            "type": "text",
                            "text": "ใช่" if language == 'thailand' else "Yes"
                                        if cutter_registration else
                                            "ไม่มี" if language == 'thailand' else "No",
                            "wrap": True,
                            "align": "end",
                            "color": "#000000",
                            "size": "xs",
                            "flex": 0
                        }
                    ]
                },
                {
                    "type": "box",
                    "layout": "horizontal",
                    "contents": [
                        {
                            "type": "text",
                            "text": "ทะเบียนรถนักคัดนักตัด" if language == 'thailand' else "Car registeration\nnumber of cutter",
                            "size": "xs",
                            "color": "#8E8E93",
                            "wrap": True
                        },
                        {
                            "type": "image",
                            "url": f"{vehicle_image_link}",
                            "align": "end",
                            "gravity": "center",
                            "flex": 0,
                            "action": {
                                "type": "uri",
                                "uri": f"{vehicle_image_link}"
                            }
                        }
                    ],
                    "justifyContent": "space-between",
                    "alignItems": "center"
                },
                {
                    "type": "box",
                    "layout": "baseline",
                    "spacing": "sm",
                    "contents": [
                        {
                            "type": "text",
                            "text": "หมายเลขทะเบียนรถ" if language == 'thailand' else "Vehicle registration number",
                            "wrap": True,
                            "color": "#8E8E93",
                            "size": "xs"
                        },
                        {
                            "type": "text",
                            "text": f"{vehicle_registration_number}",
                            "wrap": True,
                            "align": "end",
                            "color": "#000000",
                            "size": "xs"
                        }
                    ]
                },
                {
                    "type": "box",
                    "layout": "baseline",
                    "spacing": "sm",
                    "contents": [
                        {
                            "type": "text",
                            "text": "จังหวัดหมายเลขทะเบียนรถ" if language == 'thailand' else "Registered province of vehicle",
                            "wrap": True,
                            "color": "#8E8E93",
                            "size": "xs"
                        },
                        {
                            "type": "text",
                            "text": f"{vehicle_registration_province}",
                            "wrap": True,
                            "align": "end",
                            "color": "#000000",
                            "size": "xs"
                        }
                    ]
                }
            ],
            "backgroundColor": "#EFF6FF",
            "paddingAll": "xl",
            "cornerRadius": "md",
            "spacing": "lg"
        }
    }


def create_flex_message_ask_cutter_photo(language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": "Please upload a photo of the durian cutter." if language == 'english' else "กรุณาอัพโหลดภาพของผู้ตัดทุเรียน",
                    "wrap": True
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "📷 Take Photo" if language == 'english' else "📷 ถ่ายรูป",
                        "uri": "line://nv/camera/"
                    },
                    "style": "primary",
                    "color": "#3B82F6",
                    "height": "sm",
                    "margin": "lg"
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "button",
                            "action": {
                                "type": "uri",
                                "label": "📂 Browse File" if language == 'english' else "📂 เรียกดูไฟล์",
                                "uri": "line://nv/cameraRoll/single"
                            },
                            "color": "#3B82F6",
                            "height": "sm"
                        }
                    ],
                    "margin": "md",
                    "cornerRadius": "md",
                    "borderColor": "#3B82F6",
                    "borderWidth": "1px",
                    "backgroundColor": "#EFF6FF"
                },
                {
                    "type": "button",
                    "action": {
                        "type": "message",
                        "label": "Continue Conversation" if language == 'english' else "ดำเนินการสนทนาต่อ",
                        "text": "skip"
                    },
                    "margin": "md",
                    "color": "#000000"
                }
            ]
        }
    }


def create_flex_message_ask_cutter_photo_again(language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": "Can not recognize cutter, please re-take photo or continue the conversation." if language == 'english' else "ไม่สามารถระบุตัวเครื่องตัดได้ กรุณาถ่ายรูปใหม่อีกครั้งหรือดำเนินการสนทนาต่อ",
                    "wrap": True
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "📷 Take Photo" if language == 'english' else "📷 ถ่ายรูป",
                        "uri": "line://nv/camera/"
                    },
                    "style": "primary",
                    "color": "#3B82F6",
                    "height": "sm",
                    "margin": "lg"
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "button",
                            "action": {
                                "type": "uri",
                                "label": "📂 Browse File" if language == 'english' else "📂 เรียกดูไฟล์",
                                "uri": "line://nv/cameraRoll/single"
                            },
                            "color": "#3B82F6",
                            "height": "sm"
                        }
                    ],
                    "margin": "md",
                    "cornerRadius": "md",
                    "borderColor": "#3B82F6",
                    "borderWidth": "1px",
                    "backgroundColor": "#EFF6FF"
                },
                {
                    "type": "button",
                    "action": {
                        "type": "message",
                        "label": "Continue Conversation" if language == 'english' else "ดำเนินการสนทนาต่อ",
                        "text": "skip"
                    },
                    "margin": "md",
                    "color": "#000000"
                }
            ]
        }
    }


def create_flex_message_ask_durian_photo(language: str, allow_skip: bool = False):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": get_displayed_texts("Ask durian photo", language)[0],
                    "wrap": True
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "📷 Take Photo" if language == 'english' else "📷 ถ่ายรูป",
                        "uri": "line://nv/camera/"
                    },
                    "style": "primary",
                    "color": "#3B82F6",
                    "height": "sm",
                    "margin": "lg"
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "button",
                            "action": {
                                "type": "uri",
                                "label": "📂 Select Image" if language == 'english' else "📂 เลือกรูปภาพ",
                                "uri": "line://nv/cameraRoll/multi"
                            },
                            "color": "#3B82F6",
                            "height": "sm"
                        }
                    ],
                    "margin": "md",
                    "cornerRadius": "md",
                    "borderColor": "#3B82F6",
                    "borderWidth": "1px",
                    "backgroundColor": "#EFF6FF"
                }] + ([
                {
                    "type": "button",
                    "action": {
                        "type": "message",
                        "label": "Skip" if language == 'english' else "ข้าม",
                        "text": "Skip" if language == 'english' else "ข้าม"
                    },
                    "margin": "md",
                    "color": "#000000"
                }] if allow_skip else [])
        }
    }


def create_flex_message_ask_upload_single_photo(display_message: str, language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": display_message,
                    "wrap": True
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "📷 Take Photo" if language == 'english' else "📷 ถ่ายรูป",
                        "uri": "line://nv/camera/"
                    },
                    "style": "primary",
                    "color": "#3B82F6",
                    "height": "sm",
                    "margin": "lg"
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "button",
                            "action": {
                                "type": "uri",
                                "label": "📂 Select Image" if language == 'english' else "📂 เลือกรูปภาพ",
                                "uri": "line://nv/cameraRoll/single"
                            },
                            "color": "#3B82F6",
                            "height": "sm"
                        }
                    ],
                    "margin": "md",
                    "cornerRadius": "md",
                    "borderColor": "#3B82F6",
                    "borderWidth": "1px",
                    "backgroundColor": "#EFF6FF"
                }
            ]
        }
    }


def create_flex_message_ask_upload_multiple_photos(display_message: str, language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": display_message,
                    "wrap": True
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "📷 Take Photo" if language == 'english' else "📷 ถ่ายรูป",
                        "uri": "line://nv/camera/"
                    },
                    "style": "primary",
                    "color": "#3B82F6",
                    "height": "sm",
                    "margin": "lg"
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "button",
                            "action": {
                                "type": "uri",
                                "label": "📂 Select Image" if language == 'english' else "📂 เลือกรูปภาพ",
                                "uri": "line://nv/cameraRoll/multi"
                            },
                            "color": "#3B82F6",
                            "height": "sm"
                        }
                    ],
                    "margin": "md",
                    "cornerRadius": "md",
                    "borderColor": "#3B82F6",
                    "borderWidth": "1px",
                    "backgroundColor": "#EFF6FF"
                }
            ]
        }
    }


def create_login_button(language: str, login_url: str):
    return [
        {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "Please login to continue" if language == 'english' else "กรุณาเข้าสู่ระบบเพื่อดำเนินการต่อ",
                        "wrap": True
                    }
                ]
            }
        }
        ,{
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "image",
                        "url": LOGIN_LOGOUT_LOGO
                    },
                    {
                        "type": "button",
                        "action": {
                            "type": "uri",
                            "label": "Login" if language == 'english' else "เข้าสู่ระบบ",
                            "uri": login_url
                        }
                    }
                ]
            }
        }
    ]


def create_relogin_button(language: str, login_url: str):
    return [
        {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "text",
                        "text": "You can re-login through the button below." if language == 'english' else "สามารถเข้าระบบใหม่ได้โดยใช้ปุ่มด้านล่าง",
                        "wrap": True
                    }
                ]
            }
        },
        {
            "type": "bubble",
            "body": {
                "type": "box",
                "layout": "vertical",
                "contents": [
                    {
                        "type": "image",
                        "url": LOGIN_LOGOUT_LOGO
                    },
                    {
                        "type": "button",
                        "action": {
                            "type": "uri",
                            "label": "Login" if language == 'english' else "เข้าสู่ระบบ",
                            "uri": login_url
                        }
                    }
                ]
            }
        }
    ]


def create_logout_button(language: str, logout_url: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "image",
                    "url": LOGIN_LOGOUT_LOGO
                },
                {
                    "type": "button",
                    "action": {
                        "type": "uri",
                        "label": "Logout" if language == 'english' else "ออกจากระบบ",
                        "uri": logout_url
                    }
                }
            ]
        }
    }

def create_flex_message_ask_location(display_message: str, display_button: str, language: str):
    return {
        "type": "bubble",
        "body": {
            "type": "box",
            "layout": "vertical",
            "contents": [
                {
                    "type": "text",
                    "text": display_message,
                    "wrap": True
                },
                {
                    "type": "box",
                    "layout": "vertical",
                    "contents": [
                        {
                            "type": "text",
                            "text": f"📍 {display_button}",
                            "align": "center",
                            "color": "#FFFFFF"
                        }
                    ],
                    "margin": "xl",
                    "paddingAll": "md",
                    "backgroundColor": "#3B82F6",
                    "cornerRadius": "sm",
                    "action": {
                        "type": "uri",
                        "label": "action",
                        "uri": "https://line.me/R/nv/location/"
                    }
                }
            ]
        }
    }
