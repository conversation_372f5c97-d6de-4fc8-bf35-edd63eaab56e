from dateutil import parser
from datetime import datetime

def thai_to_gregorian(date_obj: datetime) -> datetime:
    """Convert Thai date to Gregorian date (subtract 543 years)."""
    if not date_obj:
        return date_obj
    return date_obj.replace(year=date_obj.year - 543)

def gregorian_to_thai(date_obj: datetime) -> datetime:
    """Convert Gregorian date to Thai date (add 543 years)."""
    if not date_obj:
        return date_obj
    return date_obj.replace(year=date_obj.year + 543)

def check_date_or_datetime(date_string):
    date_format = "%Y-%m-%d"  # Example: 2025-03-28
    datetime_format = "%Y-%m-%dT%H:%M"  # Example: 2025-03-28T11:23

    try:
        datetime.strptime(date_string, datetime_format)
        return "Datetime"
    except ValueError:
        pass

    try:
        datetime.strptime(date_string, date_format)
        return "Date"
    except ValueError:
        pass

    return "Invalid format"

def format_date(date_str: str, language: str = "thailand"):
    """Format date string to readable format in Thai calendar."""
    if not date_str:
        return "Not set"

    if check_date_or_datetime(date_str) == 'Datetime':
        date_obj = datetime.strptime(date_str, '%Y-%m-%dT%H:%M')
    elif check_date_or_datetime(date_str) == 'Date':
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    else:
        return "Invalid format"

    thai_date = gregorian_to_thai(date_obj)
    if language == 'thailand':
        return thai_date.strftime('%Y-%m-%d') + " พ.ศ."
    return thai_date.strftime('%B %d, %Y') + " BE"  # e.g., March 18, 2568

def convert_to_datetime(date_str: str):
    return parser.parse(date_str)
