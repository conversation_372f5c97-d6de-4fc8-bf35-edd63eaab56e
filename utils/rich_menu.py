import logging
import requests
import time
import json
from PIL import Image, ImageDraw, ImageFont

from utils.config import LINE_BOT_ID, LINE_CHANNEL_ACCESS_TOKEN, ENV, M_WEB_APP_URL
from utils.logger import logger

def create_and_set_rich_menu():
    """Create, upload image, and set a rich menu as default."""
    headers = {
        'Authorization': f'Bearer {LINE_CHANNEL_ACCESS_TOKEN}'
    }

    try:
        # Delete existing menus
        url = 'https://api.line.me/v2/bot/richmenu/list'
        response = requests.get(url, headers=headers)
        if response.ok:
            menus = response.json().get('richmenus', [])
            for menu in menus:
                delete_url = f'https://api.line.me/v2/bot/richmenu/{menu["richMenuId"]}'
                requests.delete(delete_url, headers=headers)
                logger.info(f"Deleted menu: {menu['richMenuId']}")
        time.sleep(2)  # Wait after deletion

        # Create new menu
        rich_menu_data = {
            "size": {
                "width": 2500,
                "height": 843
            },
            "selected": True,
            "name": "Durian Bot Menu",
            "chatBarText": "Menu" if ENV == 'dev' else "เมนู",
            "areas": [
                {
                    "bounds": {
                        "x": 0,
                        "y": 0,
                        "width": 833,
                        "height": 843
                    },
                    "action": {
                        "type": "message",
                        "text": "Record Durian" if ENV == 'dev' else "บันทึกทุเรียน",
                        "label": "Record Durian" if ENV == 'dev' else "บันทึกทุเรียน"
                    }
                },
                {
                    "bounds": {
                        "x": 833,
                        "y": 0,
                        "width": 833,
                        "height": 843
                    },
                    "action": {
                        "type": "uri",
                        "label": "View harvest history" if ENV == 'dev' else "ประวัติการเก็บเกี่ยวทุเรียน",
                        "uri": f"{M_WEB_APP_URL}/main?channel=line-app&line_bot_id={LINE_BOT_ID}"
                    }
                    # "action": {
                    #     "type": "message",
                    #     "text": "View harvest history" if ENV == 'dev' else "ประวัติการเก็บเกี่ยวทุเรียน",
                    #     "label": "View history"
                    # }
                },
                {
                    "bounds": {
                        "x": 1666,
                        "y": 0,
                        "width": 834,
                        "height": 843
                    },
                    "action": {
                        "type": "message",
                        "text": "Contact us" if ENV == 'dev' else "ติดต่อเรา",
                        "label": "Contact us" if ENV == 'dev' else "ติดต่อเรา"
                    }
                }
            ]
        }

        headers['Content-Type'] = 'application/json'
        response = requests.post(
            'https://api.line.me/v2/bot/richmenu',
            headers=headers,
            data=json.dumps(rich_menu_data)
        )
        response.raise_for_status()
        rich_menu_id = response.json()['richMenuId']
        logger.info(f"Created rich menu with ID: {rich_menu_id}")

        # Create menu image
        # img = Image.new('RGB', (2500, 843), '#FFFFFF')
        # draw = ImageDraw.Draw(img)
        
        # # Draw borders
        # draw.line([(833, 0), (833, 843)], fill='#000000', width=2)
        # draw.line([(1666, 0), (1666, 843)], fill='#000000', width=2)
        
        # try:
        #     font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', 60)
        # except:
        #     font = ImageFont.load_default()

        # sections = ['Record Durian', 'View harvest history', 'Contact us']
        # widths = [833, 833, 834]
        # for i, text in enumerate(sections):
        #     x = sum(widths[:i])
        #     w = widths[i]
        #     bbox = draw.textbbox((0, 0), text, font=font)
        #     text_width = bbox[2] - bbox[0]
        #     text_height = bbox[3] - bbox[1]
        #     draw.text(
        #         (x + (w - text_width) / 2, (843 - text_height) / 2),
        #         text, font=font, fill='#000000', align='center'
        #     )

        # # Save image with UTF-8 encoding support
        # img.save('rich_menu.png', 'PNG')
        # logger.info("Created rich menu image")

        # Upload image with retries
        max_retries = 3
        retry_delay = 5  # seconds
        success = False

        for attempt in range(max_retries):
            try:
                # Wait before upload
                time.sleep(retry_delay * (attempt + 1))
                
                with open('rich_menu.png', 'rb') as f:
                    image_data = f.read()
                    headers['Content-Type'] = 'image/png'
                    headers['Content-Length'] = str(len(image_data))
                    response = requests.post(
                        f'https://api-data.line.me/v2/bot/richmenu/{rich_menu_id}/content',
                        headers=headers,
                        data=image_data
                    )
                    response.raise_for_status()
                    logger.info("Uploaded rich menu image")
                    success = True
                    break
            except requests.exceptions.RequestException as e:
                logger.error(f"Upload attempt {attempt + 1} failed: {str(e)}")
                if hasattr(e.response, 'text'):
                    logger.error(f"Response: {e.response.text}")
                if attempt == max_retries - 1:
                    raise

        if success:
            # Set as default with retries
            for attempt in range(max_retries):
                try:
                    time.sleep(retry_delay * (attempt + 1))
                    response = requests.post(
                        f'https://api.line.me/v2/bot/user/all/richmenu/{rich_menu_id}',
                        headers={'Authorization': f'Bearer {LINE_CHANNEL_ACCESS_TOKEN}'}
                    )
                    response.raise_for_status()
                    logger.info("Set rich menu as default")
                    return rich_menu_id
                except requests.exceptions.RequestException as e:
                    logger.error(f"Set default attempt {attempt + 1} failed: {str(e)}")
                    if hasattr(e.response, 'text'):
                        logger.error(f"Response: {e.response.text}")
                    if attempt == max_retries - 1:
                        raise

    except Exception as e:
        logger.error(f"Rich menu creation failed: {str(e)}")
        raise

def verify_rich_menu(menu_id):
    """Verify rich menu is properly set."""
    headers = {'Authorization': f'Bearer {LINE_CHANNEL_ACCESS_TOKEN}'}
    
    # Check if menu exists
    response = requests.get(
        f'https://api.line.me/v2/bot/richmenu/{menu_id}',
        headers=headers
    )
    if not response.ok:
        logger.error("Rich menu not found")
        return False

    # Check if it's set as default
    response = requests.get(
        'https://api.line.me/v2/bot/user/all/richmenu',
        headers=headers
    )
    if not response.ok or response.json().get('richMenuId') != menu_id:
        logger.error("Rich menu not set as default")
        return False

    logger.info("Rich menu verified successfully")
    return True
