import os
from dotenv import load_dotenv
import urllib.parse

load_dotenv()

ENV = os.getenv('ENV', 'staging')
API_KEY = os.getenv('API_KEY')

# LINE Bot configuration
# LINE API Credentials
LINE_CHANNEL_SECRET = os.getenv('LINE_CHANNEL_SECRET')
LINE_CHANNEL_ACCESS_TOKEN = os.getenv('LINE_CHANNEL_ACCESS_TOKEN')
LINE_API_URL = os.getenv('LINE_API_URL')
LINE_BOT_ID = urllib.parse.quote(os.getenv('LINE_BOT_ID', ''))

# PTP API Configuration
DURIAN_API_URL = os.getenv('DURIAN_API_URL', None)
UPLOAD_FILE_TOKEN = os.getenv('UPLOAD_FILE_TOKEN', None)
CONTROLLER_API_KEY = os.getenv('CONTROLLER_API_KEY', None)

# File upload configuration
UPLOAD_FILE_ENDPOINT = os.getenv('UPLOAD_FILE_ENDPOINT', None)
FILE_LINK_PREFIX = os.getenv('FILE_LINK_PREFIX', None)

# Web app configuration
M_WEB_APP_URL = os.getenv('M_WEB_APP_URL', 'https://m-client.ptp-dev.vnsilicon.site')


# Database configuration
MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017')
MONGODB_DB_NAME = os.getenv('MONGODB_DB_NAME', 'notification' if ENV == 'dev' else 'line_bot')

# Redis configuration
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')

LOGIN_LOGOUT_LOGO = "https://directus-asset.staging.vnsilicon.site/2f4388de-d2e4-4d7e-b1dc-d348af040b77.png"