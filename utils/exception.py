class CharacterLimitExceededError(Exception):
    """Exception raised when the character limit is exceeded."""

    def __init__(self, value, message="Character limit exceeded", step=''):
        self.value = value
        self.message = message
        self.step = step
        super().__init__(f"{self.step}: {message}: {value}")

class DataExistsError(Exception):
    """Exception raised when the data already exists."""

    def __init__(self, value, message="Data already exists", step=''):
        self.value = value
        self.message = message
        self.step = step
        super().__init__(f"{self.step}: {message}: {value}")

class SpecialCharacterError(Exception):
    """Exception raised when the input contains special characters."""

    def __init__(self, value, message="Input contains special characters", step=''):
        self.value = value
        self.message = message
        self.step = step
        super().__init__(f"{self.step}: {message}: {value}")