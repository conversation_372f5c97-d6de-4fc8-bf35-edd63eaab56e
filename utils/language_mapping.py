import csv

HARVEST_LANGUAGE_MAPPING = {
    "Contact us": {
        "english": ["""Do you need help with the Smart Durian Traceability System?

Contact us via
<EMAIL>

We'll respond within 1–2 business days. Thank you!

In your mail, please include:
- Your name and your login credential (phone or email):
- Brief issue or request description:
- Screenshots (if applicable):"""],
        "thailand": ["""คุณต้องการความช่วยเหลือเกี่ยวกับ "ระบบตรวจสอบย้อนกลับทุเรียนอัจฉริยะ" หรือไม่?

กรุณาติดต่อเราได้ที่
<EMAIL>

เราจะตอบกลับภายใน 1-2 วันทำการ ขอขอบคุณครับ

ในอีเมลของท่าน กรุณาระบุข้อมูลต่อไปนี้:
- ชื่อของท่าน และข้อมูลเข้าสู่ระบบ (หมายเลขโทรศัพท์หรืออีเมล)
- คำอธิบายโดยย่อเกี่ยวกับปัญหาหรือคำขอ
- ภาพหน้าจอ (ถ้ามี)"""]
    },
    "Instruction for farmer": {
        "english": ["""The system will request the following durian harvest information:

1. Batch name
2. Plot
3. Durian variety
4. Weight by grade
5. Cutting date
6. Blooming date
7. Cutter
8. Packing house"""],
        "thailand": ["""ระบบจะขอข้อมูลการเก็บเกี่ยวทุเรียน
จากท่านดังนี้:

1. ชื่อล็อตการเก็บเกี่ยว
2. แปลงปลูก
3. สายพันธุ์ทุเรียน
4. น้ำหนักตามเกรด
5. วันที่ตัดทุเรียน
6. วันที่ดอกบาน
7. นักคัดนักตัด
8. ล้งที่รับซื้อ"""]
    },
    "Instruction for cutter": {
        "english": ["""The system will request the following durian harvest information:

1. Batch name
2. Farm name
3. Plot
4. Durian variety
5. Weight by grade
6. Cutting date
7. Blooming date
8. Vehicle registration
9. Packing house"""],
        "thailand": ["""ระบบจะขอข้อมูลการเก็บเกี่ยวทุเรียน
จากท่านดังนี้:

1. ชื่อล็อตการเก็บเกี่ยว
2. ชื่อสวนทุเรียน
3. แปลงปลูก
4. สายพันธุ์ทุเรียน
5. น้ำหนักตามเกรด
6. วันที่ตัดทุเรียน
7. วันที่ดอกบาน
8. ป้ายทะเบียนรถนักคัดนักตัด
9. ล้งที่รับซื้อ"""]
    },
    "Ask harvest date": {
        "english": ["What day did you cut the durian?"],
        "thailand": ["ท่านตัดทุเรียนในวันที่เท่าไร?"]
    },
    "Confirm harvest day": {
        "english": ["Harvest date set to: "],
        "thailand": ["วันที่เก็บเกี่ยวถูกตั้งเป็น: "]
    },
    "Ask blooming date": {
        "english": ["What day does durian bloom?"],
        "thailand": ["วันที่ดอกบานของทุเรียนคือวันไหน?"]
    },
    "Confirm blooming day": {
        "english": ["Blooming date set to: "],
        "thailand": ["วันที่ดอกบานถูกตั้งเป็น: "]
    },
    "Ask durian type": {
        "english": ["What type of durian do you harvest?"],
        "thailand": ["ทุเรียนป็นพันธุ์อะไรครับ?"]
    },
    "Ask durian weight": {
        "english": ["What is the total weight (in kilograms)?"],
        "thailand": ["น้ำหนักรวมทั้งหมด (กิโลกรัม) เท่าไหร่ครับ"]
    },
    "Ask durian grade": {
        "english": ["What grade of durian is this?"],
        "thailand": ["เกรดทุเรียนอะไรครับ"]
    },
    "Ask packing house": {
        "english": ["Please select the packing house you are selling."],
        "thailand": ["โปรดระบุล้งที่ท่านนำไปขาย"]
    },
    "Ask cutter selection": {
        "english": ["Please select the cutter."],
        "thailand": ["โปรดเลือกนักคัดนักตัดทุเรียนครับ"]
    },
    "Ask cutter photo": {
        "english": ["Please take a photo of durian cutter."],
        "thailand": ["โปรดเพิ่มรูปภาพนักคัดนักตัด"]
    },
    "Ask DOA certifiacte": {
        "english": ["Please enter the DOA certificate number of the durian cutter"],
        "thailand": ["หมายเลขใบรับรอง DOA"]
    },
    "Ask vehicle plate": {
        "english": ["Please enter the vehicle registration number"],
        "thailand": ["โปรดระบุป้ายทะเบียนรถ"]
    },
    "Ask durian photo": {
        "english": ["Please upload photos of the durian (maximum 3 photos)."],
        "thailand": ["กรุณาอัพโหลดภาพการเก็บเกี่ยวทุเรียน (ไม่เกิน 3 รูป)"]
    },
    "Invalid input": {
        "english": ["❌ Invalid input. Please follow the instructions."],
        "thailand": ["❌ ข้อมูลไม่ถูกต้อง กรุณากรอกตามคำแนะนำ"]
    },
    "Warning blooming date": {
        "english": ["⚠️ This {variety_name} is too early to cut, should more than {variety_allow_day} days. Do you want to change the date?"],
        "thailand": ["⚠️ {variety_name} นี้เร็วเกินไปที่จะตัด ควรตัดเกิน {variety_allow_day} วัน คุณต้องการเปลี่ยนวันที่หรือไม่"]
    },
    "Invalid blooming date": {
        "english": ["❌ Invalid blooming date. Please do not enter a date in the future and must be before the cutting day."],
        "thailand": ["❌ วันที่ดอกบานไม่ถูกต้อง กรุณาอย่าใส่วันที่ในอนาคต และต้องระบุก่อนวันที่ตัดทุเรียน"]
    },
    "Invalid harvest date": {
        "english": ["❌ Invalid harvest date. Please do not enter a date in the future."],
        "thailand": ["❌ วันที่เก็บเกี่ยวไม่ถูกต้อง กรุณาอย่าใส่วันที่ในอนาคต"]
    },
    "Invalid vehicle registration number": {
        "english": ["❌ Invalid vehicle registration number. Please enter a valid Thailand vehicle registration number."],
        "thailand": ["❌ ป้ายทะเบียนรถไม่ถูกต้อง กรุณากรอกป้ายทะเบียนรถที่ถูกต้อง"]
    },
    "Harvest record not found": {
        "english": ["❌ No harvest record found. Maybe your record has been deleted."],
        "thailand": ["❌ ไม่พบข้อมูลการเก็บเกี่ยว อาจจะเป็นเพราะข้อมูลของคุณถูกลบไปแล้ว"]
    },
    "Cannot save as draft": {
        "english": ["❌ Cannot save as draft. You are unable to save a published record as draft."],
        "thailand": ["❌ ไม่สามารถบันทึกฉบับร่างได้"]
    },
    "Error processing text message": {
        "english": ["Sorry, there was an error processing your message. Please try again later."],
        "thailand": ["ขออภัย ระบบมีปัญหาในการประมวลผลข้อมูลของคุณ กรุณาลองใหม่อีกครั้งในภายหลัง"]
    },
    "Ask for confirmation": {
        "english": ["Please confirm the accuracy of the information."],
        "thailand": ["โปรดยืนยันข้อมูลความถูกต้อง"]
    },
    "Button confirm information": {
        "english": ["Confirm information"],
        "thailand": ["ยืนยันข้อมูล"]
    },
    "Button edit information": {
        "english": ["Edit"],
        "thailand": ["แก้ไข"]
    },
    "Durian traceability system": {
        "english": ["Durian traceability system"],
        "thailand": ["ระบบตรวจสอบย้อนกลับทุเรียน"]
    },
    "Durian traceability number": {
        "english": ["Durian traceability number"],
        "thailand": ["เลขที่ตรวจสอบย้อนกลับทุเรียน"]
    },
    "Durian cutter": {
        "english": ["Durian cutter"],
        "thailand": ["นักคัดนักตัด"]
    },
    "Packing house": {
        "english": ["Packing house"],
        "thailand": ["ล้งที่รับซื้อ"]
    },
    "Durian grade": {
        "english": ["Durian grade"],
        "thailand": ["เกรดทุเรียน"]
    },
    "Durian weight": {
        "english": ["Durian weight"],
        "thailand": ["น้ำหนักรวม"]
    },
    "Harvest date": {
        "english": ["Harvest date"],
        "thailand": ["วันที่เก็บเกี่ยว"]
    },
    "Blooming date": {
        "english": ["Blooming date"],
        "thailand": ["วันที่ดอกบาน"]
    },
    "Durian type": {
        "english": ["Durian type"],
        "thailand": ["พันธุ์ทุเรียน"]
    },
    "Welcome message": {
        "english": ["Welcome! Use the menu below to start."],
        "thailand": ["ยินดีต้อนรับ! ใช้เมนูด้านล่างนี้เพื่อเริ่มต้น"]
    },
    "Connect DOA message": {
        "english": ["Please connect the DOA in your region."],
        "thailand": ["กรุณาเชื่อมต่อ DOA ในภูมิภาคของคุณ"]
    },
    "Received durian image message": {
        "english": ["I received your durian images. Thank you!"],
        "thailand": ["ได้รับรูปทุเรียนของคุณแล้ว ขอบคุณครับ!"]
    },
    "Received cutter image message": {
        "english": ["I received your cutter images. Thank you!"],
        "thailand": ["ได้รับรูปนักคัดนักตัดของคุณแล้ว ขอบคุณครับ!"]
    },
    "Error processing image message": {
        "english": ["Sorry, there was an error processing your image. Please try again later."],
        "thailand": ["Sorry, there was an error processing your image. Please try again later."]
    },
    "Cancel message": {
        "english": ["Operation cancelled. You can continue using the menu."],
        "thailand": ["ยกเลิกการดำเนินการแล้ว คุณสามารถใช้เมนูต่อได้"]
    },
    "Ask vehicle photo": {
        "english": ["Please attach a photo of the license plate of the vehicle that transported the durians from farm."],
        "thailand": ["โปรดแนบรูปภาพป้ายทะเบียนรถที่ขนทุเรียนออกจากสวน"]
    },
    "Ask vehicle registration number": {
        "english": ["Please enter the vehicle registration number"],
        "thailand": ["โปรดระบุป้ายทะเบียนรถ"]
    },
    "Ask vehicle registration province": {
        "english": ["Please provide the vehicle registration province"],
        "thailand": ["โปรดระบุจังหวัดในป้ายทะเบียนรถ"]
    },
    "Ask send or draft": {
        "english": ["Do you want to send this information to the packing house?"],
        "thailand": ["คุณต้องการส่งข้อมูลนี้ไปที่ล้งที่รับซื้อหรือไม่?"]
    },
    "Send packing house": {
        "english": ["Send to packing house"],
        "thailand": ["ส่งให้ล้งที่รับซื้อ"]
    },
    "Draft action": {
        "english": ["Save as draft"],
        "thailand": ["บันทึกเป็นฉบับร่าง"]
    },
    "Confirm action": {
        "english": ["Confirm and send"],
        "thailand": ["ยืนยัน และส่งข้อมูล"]
    },
    "Edit action": {
        "english": ["Edit information"],
        "thailand": ["แก้ไขข้อมูล"]
    },
    "Ask cutter name": {
        "english": ["Please provide the first and last name of the cutter."],
        "thailand": ["โปรดระบุ ชื่อ-นามสกุล ของนักคัดนักตัดครับ"]
    },
    "Ask cutter registration": {
        "english": ["Is he a certified cutter?"],
        "thailand": ["ขึ้นทะเบียนักคัดนักตัดแล้วหรือไม่?"]
    },
    "Ask cutter registration number": {
        "english": ["Please specify the registration number of the cutter."],
        "thailand": ["โปรดระบุ เลขทะเบียนนักคัดนักตัด"]
    },
    "Cutter not registered error": {
        "english": ["Sorry, unregistered cutters are not allowed to harvest"],
        "thailand": ["ขออภัย นักคัดนักตัดที่ไม่มีใบรับรองไม่สามารถตัดทุเรียนได้"]
    },
    "Yes": {
        "english": ["Yes"],
        "thailand": ["ใช่"]
    },
    "No": {
        "english": ["No"],
        "thailand": ["ไม่มี"]
    },
    "Permission denied": {
        "english": ["You don't have permission to record durians. Only farmers and cutters can use this feature."],
        "thailand": ["คุณไม่มีสิทธิ์ในการบันทึกทุเรียน เฉพาะเกษตรกรและนักคัดนักตัดเท่านั้นที่สามารถใช้ฟีเจอร์นี้ได้"]
    },
    "Ask harvest lot name": {
        "english": ["Please specify your batch name."],
        "thailand": ["โปรดระบุชื่อล็อตการเก็บเกี่ยวของท่าน"]
    },
    "Ask plot selection": {
        "english": ["Please select the plot to harvest in this garden."],
        "thailand": ["กรุณาเลือกแปลงปลูกในสวนนี้"]
    },
    "Ask additional plots": {
        "english": ["Are there any other plots?"],
        "thailand": ["มีแปลงปลูกอื่นๆอีกไหม"]
    },
    "Ask variety selection": {
        "english": ["Please select the durian variety:"],
        "thailand": ["กรุณาเลือกพันธุ์ทุเรียน:"]
    },
    "Ask variety name": {
        "english": ["Please specify the durian variety name"],
        "thailand": ["โปรดระบุชื่อพันธุ์ทุเรียน"]
    },
    "Ask additional varieties": {
        "english": ["Do you have any other durian varieties?"],
        "thailand": ["มีทุเรียนพันธุ์อื่นๆอีกไหม"]
    },
    "Ask blooming date per variety": {
        "english": ["What day does {variety} durian bloom?"],
        "thailand": ["วันที่ดอกบานของทุเรียน{variety}คือวันไหน?"]
    },
    "Ask grade weight per variety": {
        "english": ["What grades of {variety} do you collect?"],
        "thailand": ["ท่านเก็บทุเรียน{variety}เกรดอะไรบ้าง?"]
    },
    "Ask weight per variety": {
        "english": ["How many kilograms does {grade} weigh?"],
        "thailand": ["น้ำหนัก {grade} กี่กิโลกรัมครับ?"]
    },
    "Ask additional grades": {
        "english": ["Do you have other grades of durian?"],
        "thailand": ["มีทุเรียนเกรดอื่นๆอีกไหมครับ"]
    },
    "Ask final confirmation": {
        "english": ["Please confirm the information accuracy"],
        "thailand": ["โปรดยืนยันข้อมูลความถูกต้อง"]
    },
    "Ask farm selection": {
        "english": ["Please select a farm you are harvesting from."],
        "thailand": ["กรุณาเลือกสวนที่ท่านไปเก็บเกี่ยวทุเรียน"]
    },
    "Ask plot certificate image": {
        "english": ["Please upload the certificate of plot {plot_name}"],
        "thailand": ["กรุณาอัพโหลดใบรับรองแปลงปลูก {plot_name}"]
    },
    "Ask adding farm name": {
        "english": ["Please type the name of farm."],
        "thailand": ["กรุณาระบุ ชื่อสวนทุเรียนครับ"]
    },
    "Ask adding farm location": {
        "english": ["Please provide the location of the farm."],
        "thailand": ["โปรดระบุที่อยู่ของสวนทุเรียนครับ"]
    },
    "Ask adding plot name": {
        "english": ["Please specify the plot name."],
        "thailand": ["โปรดระบุเลขแปลงปลูก ครับ"]
    },
    "Ask adding plot gap": {
        "english": ["Please specify the GAP number."],
        "thailand": ["โปรดระบุเลขทะเบียน GAP"]
    },
    "Ask adding plot area": {
        "english": ["Please specify the area (in Rai and only enter number)."],
        "thailand": ["โปรดระบุพื้นที่ (เป็นไร่ และกรอกเฉพาะตัวเลขเท่านั้น)"]
    },
    "Ask adding plot id": {
        "english": ["Please specify the plot ID."],
        "thailand": ["โปรดระบุหมายเลขแปลงปลูก"]
    },
    "Ask adding plot confirmation": {
        "english": ["Plot Information\n\nPlot name: {plot_name}\nGAP: {gap}\nArea: {area} Rai\nPlot ID: {plot_id}\n\nIs this correct?"],
        "thailand": ["ข้อมูลแปลงปลูก\n\nชื่อแปลงปลู: {plot_name}\nGAP: {gap}\nพื้นที่: {area} ไร่\nหมายเลขแปลงปลูก: {plot_id}\n\nข้อมูลนี้ถูกต้องหรือไม่?"]
    },
    "Prompt save as draft": {
        "english": ["☑️ Successfully saved. You can continue by clicking the button below and editing the data then submit."],
        "thailand": ["☑️ บันทึกสำเร็จ คุณสามารถดำเนินการต่อโดยคลิกปุ่มด้านล่างเพื่อแก้ไขข้อมูล และกดส่งข้อมูล"]
    },
    "Prompt confirm and send": {
        "english": ["✅ Successfully record. The system will send the notification to related people."],
        "thailand": ["✅ บันทึกข้อมูลสำเร็จ ระบบจะส่งการแจ้งเตือนไปยังผู้ที่เกี่ยวข้อง"]
    },
    "Prompt edit and send": {
        "english": ["Click button below to edit the {harvest_lot_name} batch."],
        "thailand": ["คลิกปุ่มด้านล่างเพื่อแก้ไขชุดข้อมูล {harvest_lot_name}"]
    },
    "Out of service": {
        "english": ["Sorry, this feature is currently under maintenance."],
        "thailand": ["ขออภัย ฟีเจอร์นี้อยู่ระหว่างการปรับปรุง"]
    },
    "Character limit exceeded": {
        "english": ["❌ It seems your input is a bit too long. Please ensure it does not exceed {char_limit} characters."],
        "thailand": ["❌ ดูเหมือนว่าข้อมูลของคุณจะยาวเกินไป กรุณาตรวจสอบให้แน่ใจว่าไม่เกิน {char_limit} ตัวอักษร"]
    },
    "Data already exists": {
        "english": ["❌ This value already exists. Please input another value."],
        "thailand": ["❌ ค่านี้มีอยู่แล้ว กรุณาใส่ค่าอื่น"]
    },
    "Prompt plot not exist": {
        "english": ["🚫 This farm has no plots yet. Please contact the farmer to create one, or start a new harvest record for another farm."],
        "thailand": ["🚫 สวนทุเรียนนี้ยังไม่มีแปลงปลูกกรุณาติดต่อเกษตรกรเพื่อสร้างแปลงปลูก หรือเริ่มบันทึกการเก็บเกี่ยวใหม่สำหรับสวนทุเรียนอื่น"]
    },
    "Invalid province": {
        "english": ["❌ Invalid province. Please enter a valid Thailand province."],
        "thailand": ["❌ จังหวัดไม่เป็นจังหวัดที่ถูกต้อง กรอกจังหวัดที่เป็นจังหวัดในไทย"]
    },
    "Ask to add a new plot": {
        "english": ["Your farm has no plots yet. Please add a new plot to continue."],
        "thailand": ["สวนทุเรียนของคุณยังไม่มีแปลงปลูก กรุณาสร้างแปลงปลูกเพื่อดำเนินการต่อ"]
    },
    "Event was processed": {
        "english": ["This event has already been processed. You can view the details by clicking the button below."],
        "thailand": ["ข้อมูลนี้ได้ถูกประมวลผลแล้ว คุณสามารถดูรายละเอียดได้โดยคลิกปุ่มด้านล่าง"]
    },
    "Invalid decimal place for weight": {
        "english": ["❌ Invalid decimal place for weight. Please enter a number with at most 1 decimal place."],
        "thailand": ["❌ น้ำหนักไม่ถูกต้อง กรุณากรอกตัวเลขที่มีจุดทศนิยมไม่เกิน 1 ตำแหน่ง"]
    },
    "View harvest history": {
        "english": ["Please click the button below to view your harvest history."],
        "thailand": ["กรุณาคลิกปุ่มด้านล่างเพื่อดูประวัติการเก็บเกี่ยว"]
    },
    "Deny view history": {
        "english": ["🚫 Only farmers and cutters can view durian harvests history. You do not have permission to use this feature."],
        "thailand": ["🚫 ไม่สามารถเข้าดูประวัติการเก็บเกี่ยวได้ เฉพาะ ชาวสวนทุเรียน และ นักคัดนักตัด เท่านั้นที่สามารถดูประวัติการเก็บเกี่ยวทุเรียนได้"]
    },
    "Invalid area": {
        "english": ["Invalid area. Please enter a number greater than 0."],
        "thailand": ["พื้นที่ไม่ถูกต้อง กรุณากรอกเลขที่มากกว่า 0"]
    },
    "Invalid weight": {
        "english": ["Invalid weight. Please enter a number between 1.0 to 9,999,999.9."],
        "thailand": ["น้ำหนักไม่ถูกต้อง กรุณากรอกเลขจำนวนระหว่าง 1.0 ถึง 9,999,999.9"]
    },
    "Invalid number of digits in GAP": {
        "english": ["You entered an invalid GAP number. Please enter exactly 17 digits."],
        "thailand": ["คุณป้อนหมายเลข GAP ไม่ถูกต้อง กรุณากรอกตัวเลขให้ครบ 17 หลัก"]
    },
    "Input contains special characters": {
        "english": ["❌ Input contains special characters. Please enter a valid input."],
        "thailand": ["❌ อินพุตมีอักขระพิเศษ กรุณากรอกข้อมูลที่ถูกต้อง"]
    },
    "Invalid number of digits in plot ID": {
        "english": ["❌ Invalid number of digits in plot ID. Please enter exactly 20 digits."],
        "thailand": ["❌ จำนวนหลักของรหัสแปลงไม่ถูกต้อง กรุณากรอกให้ครบ 20 หลักพอดี"]
    }
}

def get_displayed_texts(key, language = "thailand"):
    return HARVEST_LANGUAGE_MAPPING[key][language or "thailand"]

def export_language_mapping():
    with open('language_mapping.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Key', 'English', 'Thai'])
        for key in HARVEST_LANGUAGE_MAPPING:
            writer.writerow([
                key,
                HARVEST_LANGUAGE_MAPPING[key]['english'][0],
                HARVEST_LANGUAGE_MAPPING[key]['thailand'][0]
            ])

# export_language_mapping()