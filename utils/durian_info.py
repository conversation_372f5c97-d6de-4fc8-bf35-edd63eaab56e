import json
import traceback
from typing import Any
from uuid import UUID
from pydantic import BaseModel
from service.ptp_controller_service import get_durian_common_information
from utils.logger import logger
from utils.memory_cache import MemoryCache

class LabelModel(BaseModel):
    en: str
    th: str

class VarietyGradeModel(BaseModel):
    id: UUID
    value: str
    label: LabelModel

class VarietyModel(BaseModel):
    id: UUID
    value: str
    label: LabelModel
    grades: list[VarietyGradeModel]
    flower_blooming_duration: int

class ProvinceModel(BaseModel):
    english: list[str]
    thailand: list[str]

    def __getitem__(self, language: str) -> list[str]:
        return getattr(self, language, self.thailand)

class DurianInfo:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DurianInfo, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self._initialized = True

        # Initialize list province of Thailand in both english and thai
        # Load province from file "province.json"
        self.province = ProvinceModel(**json.load(open('province.json')))
        # Sort province by name in both english and thai
        self.province.english.sort()
        self.province.thailand.sort()
        
        # Check if data exists in cache, if not fetch from API
        varieties_data = None
        try:
            varieties_data = MemoryCache.get("durian", "varieties_data")
        except Exception:
            logger.error("Failed to get durian varieties from cache.")

        if not varieties_data:
            self._fetch_durian_varieties()

    def _fetch_durian_varieties(self):
        """Fetch durian varieties from API and populate class attributes"""
        logger.info("Fetching durian varieties from API...")
        try:
            # Get durian varieties with their grades from API
            durian_common_info = get_durian_common_information('varieties')
            # # Sort varieties, moving "other" to the end
            # durian_common_info = sorted(
            #     durian_common_info,
            #     key=lambda x: (x["value"] == "other", x["value"])
            # )
            
            # Process varieties
            varieties_data = []
            valid_varieties = {}
            mapping_text_to_value = {}
            mapping_value_to_text = {}

            for variety in durian_common_info:
                try:
                    # Store raw variety data
                    variety_data = {
                        'id': variety['id'],
                        'label': variety['label'],
                        'value': variety['value'],
                        'grades': variety['grades'],
                        'flower_blooming_duration': variety['flower_blooming_duration']
                    }
                    varieties_data.append(variety_data)
                    valid_varieties[variety['label']['en']] = True
                    valid_varieties[variety['label']['th']] = True

                    # Create mapping
                    mapping_text_to_value[variety['label']['en']] = variety['value']
                    mapping_text_to_value[variety['label']['th']] = variety['value']
                    mapping_value_to_text[variety['value']] = variety['label']
                except Exception as e:
                    logger.error(f"Failed to process variety {variety}.")
                    traceback.print_exception(type(e), e, e.__traceback__)
                    continue

            # Cache the raw JSON data
            MemoryCache.set("durian", "varieties_data", varieties_data)
            MemoryCache.set("durian", "valid_varieties", valid_varieties)
            MemoryCache.set("durian", "mapping_text_to_value", mapping_text_to_value)
            MemoryCache.set("durian", "mapping_value_to_text", mapping_value_to_text)
            
            logger.info(f"Fetched {len(durian_common_info)} durian varieties.")
            return True
        except Exception as e:
            logger.error("Failed to fetch durian varieties from API.")
            traceback.print_exception(type(e), e, e.__traceback__)
            return False

    def _get_varieties_data(self) -> list[VarietyModel]:
        """Get varieties data from cache or fetch from API if not available"""
        varieties_data = MemoryCache.get("durian", "varieties_data")
        if not varieties_data:
            self._fetch_durian_varieties()
            varieties_data = MemoryCache.get("durian", "varieties_data") or []
        return [VarietyModel(**variety) for variety in varieties_data]

    def is_varieties_available(self):
        """Check if durian varieties are available.
        If not, try to fetch them from the API.
        """
        varieties_data = self._get_varieties_data()
        if not varieties_data:
            return self._fetch_durian_varieties()
        return True  # Already have varieties

    def get_variety_labels(self, language='thailand') -> list[str]:
        """Get all variety labels in the specified language."""
        varieties_data = self._get_varieties_data()
        if not varieties_data:
            return []
        labels: list[str] = []
        for variety in varieties_data:
            if language == 'english':
                labels.append(variety.label.en)
            else:
                labels.append(variety.label.th)
        return labels

    def is_valid_variety(self, variety_label: str):
        """Check if the given variety is valid."""
        valid_varieties = MemoryCache.get("durian", "valid_varieties")
        if not valid_varieties:
            if not self._fetch_durian_varieties():
                return False
            valid_varieties = MemoryCache.get("durian", "valid_varieties")
        return valid_varieties.get(variety_label, False)

    def is_valid_grade(self, variety_value: str, grade_label: str):
        """Check if the given grade label is valid for the specified variety value."""
        grades = []
        grades += self.get_grade_label_by_variety_value(variety_value, "english") or []
        grades += self.get_grade_label_by_variety_value(variety_value, "thailand") or []
        return grade_label in grades if grades else False

    def is_valid_province(self, province: str):
        """Check if the given province is valid."""
        return province in self.province.english or province in self.province.thailand

    def mapping_variety_text_to_value(self, text: str) -> str:
        mapping = MemoryCache.get("durian", "mapping_text_to_value")
        if not mapping:
            if not self._fetch_durian_varieties():
                return ''
            mapping = MemoryCache.get("durian", "mapping_text_to_value") or {}
        return mapping.get(text, '')

    def mapping_variety_value_to_text(self, value: str, language='thailand') -> str:
        mapping = MemoryCache.get("durian", "mapping_value_to_text")
        if not mapping:
            if not self._fetch_durian_varieties():
                return ''
            mapping = MemoryCache.get("durian", "mapping_value_to_text") or {}
        if value not in mapping:
            return ''
        try:
            if language == 'english':
                return mapping[value]['en']
            return mapping[value]['th']
        except Exception as e:
            logger.error(f"Failed to map variety value to text. Variety: {value}, Language: {language}")
            traceback.print_exception(type(e), e, e.__traceback__)
            return ''

    def mapping_grade_value_to_text(self, variety_value: str, grade_value: str, language='thailand') -> str:
        """Map grade value to text based on variety value and language."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                for grade in variety.grades:
                    if grade.value == grade_value:
                        if language == 'english':
                            return grade.label.en
                        return grade.label.th
        return ''

    def mapping_variety_value_to_id(self, variety_value: str) -> str | None:
        """Map variety value to ID."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                return str(variety.id)
        return None

    def mapping_grade_value_to_id(self, variety_value: str, grade_value: str) -> str | None:
        """Map grade value to ID based on variety value."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                for grade in variety.grades:
                    if grade.value == grade_value:
                        return str(grade.id)
        return None

    def get_grade_label_by_variety_label(self, variety_label: str, language='thailand') -> list[str] | None:
        """Retrieve the grades for a given variety."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.label.en == variety_label or variety.label.th == variety_label:
                if language == 'english':
                    return [grade.label.en for grade in variety.grades]
                return [grade.label.th for grade in variety.grades]
        return None

    def get_grade_label_by_variety_value(self, variety_value: str, language='thailand') -> list[str] | None:
        """Retrieve the grades for a given variety."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                if language == 'english':
                    return [grade.label.en for grade in variety.grades]
                return [grade.label.th for grade in variety.grades]
        return None

    def get_grade_value(self, variety_value: str, grade_label: str) -> str | None:
        """Retrieve the grade value for a given variety and grade label."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                for grade in variety.grades:
                    if grade.label.en == grade_label or grade.label.th == grade_label:
                        return grade.value
        return None

    def get_varieties(self) -> list[VarietyModel]:
        """Get all varieties."""
        return self._get_varieties_data()

    def get_variety_by_value(self, variety_value: str) -> VarietyModel | None:
        """Retrieve the variety object for a given variety value."""
        varieties_data = self._get_varieties_data() or []
        for variety in varieties_data:
            if variety.value == variety_value:
                return variety
        return None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

DURIAN_INFO = DurianInfo.get_instance()
