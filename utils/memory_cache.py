import json
import redis
from utils.config import REDIS_URL

EXPIRE_TIME = 3 * 24 * 3600

class MemoryCache:
    """Redis-based key-value cache with DT_LINEBOT prefix"""
    
    _redis = redis.Redis.from_url(REDIS_URL)
    _prefix = "DT_LINEBOT"
    
    @classmethod
    def _get_key(cls, id):
        """Generate prefixed key"""
        return f"{cls._prefix}:{id}"
    
    @classmethod
    def get(cls, id, field):
        """Get value from Redis hash and decode from bytes to string"""
        key = cls._get_key(id)
        value: bytes = cls._redis.hget(key, field)
        if field in ['language', 'state', 'user_role', 'signed_in']:
            return value.decode('utf-8') if value else None
        return json.loads(value.decode('utf-8')) if value else None
    
    @classmethod
    def set(cls, id, field, value):
        """Set value in Redis hash"""
        key = cls._get_key(id)
        if value is None:
            return
        if isinstance(value, bool):
            cls._redis.hset(key, field, bytes(str(value), 'utf-8'))
            cls._redis.expire(key, EXPIRE_TIME)
            return
        if not isinstance(value, (bytes, str, int, float)):
            value = json.dumps(value)
        cls._redis.hset(key, field, value)
        cls._redis.expire(key, EXPIRE_TIME)
    
    @classmethod
    def delete(cls, id, field):
        """Delete field from Redis hash"""
        key = cls._get_key(id)
        cls._redis.hdel(key, field)
        # Check if hash is empty and delete it if so
        if cls._redis.hlen(key) == 0:
            cls._redis.delete(key)

    @classmethod
    def delete_all_except(cls, id: str, excluded_fields: list[str]):
        """Delete all fields from hash except specified ones"""
        key = cls._get_key(id)
        all_fields = cls._redis.hkeys(key)
        for field in all_fields:
            if field.decode() not in excluded_fields:
                cls._redis.hdel(key, field)

    @classmethod
    def delete_all_fields(cls, id):
        """Delete entire hash"""
        key = cls._get_key(id)
        cls._redis.delete(key)
