name: PR Check

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
    branches:
      - main
  push:
    branches:
      - main
      - feat/*
      - fix/*
      - chore/*

permissions:
  # Allow access to commit list
  contents: read
  # Allow access to adding comments
  discussions: write
  pull-requests: write
jobs:
  Code-Leak-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE}}