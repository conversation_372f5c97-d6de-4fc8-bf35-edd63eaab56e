from datetime import datetime
import traceback
from bson.objectid import ObjectId
from models_mongo import MongoDB, <PERSON><PERSON><PERSON><PERSON><PERSON>, User
from utils.config import ENV
from utils.logger import logger
from utils.memory_cache import MemoryCache
from utils.time_util import thai_to_gregorian

class DATABASE_SERVICE_MONGO:
    # Singleton instance
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DATABASE_SERVICE_MONGO, cls).__new__(cls)
            cls._instance.db = MongoDB().get_db()
            # Cache for logged-in users
            logger.info("Creating MongoDB database service ...")
        return cls._instance

    def get_user_record_by_id(self, user_record_id: str) -> dict:
        user = self.db.line_users.find_one({"_id": ObjectId(user_record_id)})
        if not user:
            return None
        return user

    def get_user_record_by_user_id(self, user_id: str) -> dict:
        user = self.db.line_users.find_one({"user_id": user_id})
        if not user:
            return None
        return user

    def get_line_user(self, query: dict) -> User | None:
        user: dict | None = self.db.line_users.find_one(query)
        if not user:
            return None
        return User(line_user_id=user.get('line_user_id'), user_id=user.get('user_id'), language=user.get('language'))

    def insert_line_user(self, user_data: User) -> str:
        record = self.db.line_users.insert_one(user_data.to_dict())
        return str(record.inserted_id)

    def update_line_user(self, query: dict, updated_field: dict) -> bool:
        try:
            self.db.line_users.update_one(
                query,
                {"$set": updated_field}
            )
            return True
        except Exception as e:
            traceback.print_exc()
            return False

    def is_user_signed_in(self, line_user_id: str):
        is_signed_in = MemoryCache.get(line_user_id, 'signed_in') or False
        if is_signed_in:
            return True

        user = self.db.line_users.find_one({"line_user_id": line_user_id})
        if user and user.get('user_id', None):
            MemoryCache.set(line_user_id, 'signed_in', True)
            return True

        if not user:
            new_user = User(line_user_id=line_user_id)
            self.db.line_users.insert_one(new_user.to_dict())

        return False

    def save_durian_record(self, user_data: dict):
        # Handle variety details
        variety_details = user_data.get('variety_details', {})

        # Convert datetime strings to Python date objects
        harvest_date_thai = datetime.strptime(user_data['harvest_date'], '%Y-%m-%dT%H:%M')
        if harvest_date_thai.year > datetime.now().year + 500:
            harvest_date_thai = thai_to_gregorian(harvest_date_thai)
        
        record = DurianRecord(
            event_id=user_data.get('event_id', ''),
            harvest_lot_name=user_data.get('harvest_lot_name', ''),
            farm_id=user_data.get('farm_id', ''),
            selected_plots=user_data.get('selected_plots', []),
            variety_details=variety_details,
            harvest_date=harvest_date_thai,
            durian_image_ids=user_data.get('durian_image_ids', []),
            position_latitude=user_data.get('position_latitude', 12.3),
            position_longitude=user_data.get('position_longitude', 23.3),
            packing_house=user_data.get('packing_house', ''),
            created_at=datetime.now(),
            cutter_name=user_data.get('cutter_name', ''),
            cutter_registration=user_data.get('cutter_registration', 'no'),
            cutter_registration_number=user_data.get('cutter_registration_number'),
            cutter_image_id=user_data.get('cutter_image_id'),
            vehicle_image_id=user_data.get('vehicle_image_id'),
            vehicle_registration_number=user_data.get('vehicle_registration_number'),
            vehicle_registration_province=user_data.get('vehicle_registration_province'),
            status=user_data.get('status', 'draft')
        )
        result = self.db.line_durian_records.insert_one(record.to_dict())
        
        # Log the Durian record
        logger.info(f"Saved Durian record with id: {result.inserted_id}")

    def get_user_language(self, line_user_id: str):
        user = self.db.line_users.find_one({"line_user_id": line_user_id})
        if not user or not user.get('language'):
            return None
        return str(user['language'])

    def save_user_language(self, line_user_id: str, language: str):
        self.db.line_users.update_one(
            {"line_user_id": line_user_id},
            {"$set": {"language": language}},
            upsert=True
        )

    def save_user(self, line_user_id: str, user_id: str):
        self.db.line_users.update_one(
            {"line_user_id": line_user_id},
            {"$set": {
                "user_id": user_id,
                "language": "english" if ENV == 'dev' else 'thailand'
            }}
        )

    def delete_user_data(self, line_user_id: str):
        self.db.line_users.delete_one({"line_user_id": line_user_id})

    def delete_old_user_if_exist(self, user_id: str):
        self.db.line_users.delete_one({"user_id": user_id})
