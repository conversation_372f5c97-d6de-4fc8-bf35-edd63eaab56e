from datetime import date, datetime, timedelta, timezone
import json
from uuid import UUID
from utils.request_util import REQUEST_UTIL
from utils.logger import logger
from utils.config import DURIAN_API_URL, CONTROLLER_API_KEY
from utils.time_util import thai_to_gregorian

def get_durian_common_information(info_type):
    headers = {
        'Content-Type': 'application/json'
    }

    response = REQUEST_UTIL.get(f'{DURIAN_API_URL}/durian/{info_type}', headers=headers)
    return response.json().get('data', [])

def get_line_jwt_token(line_user_id: str) -> str | None:
    """Get LINE JWT token for a user."""
    url = f'{DURIAN_API_URL}/user/generate_line_jwt_token'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'Content-Type': 'application/json'
    }
    api_data = {
        'user_line_id': line_user_id
    }
    response = REQUEST_UTIL.post(url, headers=headers, json=api_data)
    return response.json().get('data', {}).get('access_token', None)

def get_farmer_cutter_info(user_id: str):
    """Get farmer cutter information based on image."""

    url = f'{DURIAN_API_URL}/user/me'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json()

def send_durian_event_harvest(user_id: str, user_role: str, data: dict):
    """Send durian harvest information as draft."""
    # Set timezone to GMT+7
    tz_offset = timedelta(hours=7)

    # Convert date strings to Python date objects
    harvest_date_thai = datetime.strptime(data['harvest_date'], '%Y-%m-%dT%H:%M')
    if harvest_date_thai.year > datetime.now().year + 500:
        harvest_date_thai = thai_to_gregorian(harvest_date_thai)

    cutter = {
        "name": data.get("cutter_name", None),
        "license_number": data.get("cutter_registration_number", None),
        "avatar": data.get("cutter_image_id", None),
        "existing_cutter_id": data.get("cutter_id", None),
        "existing_cutter_profile_id": data.get("cutter_profile_id", None)
    } if user_role == "farmer" else None

    cutter_vehicle = {
        "vehicle_registration_number": data['vehicle_registration_number'],
        "province_registration_number": data['vehicle_registration_province'],
        "image": data['vehicle_image_id']
    }

    farm = {
        "farm_id": data['farm_id'],
        "plots": [
            {
                "id": plot['id'],
                "image": plot['plot_certificate_image_id']
            } for plot in data.get('selected_plots', [])
        ]
    }

    varieties = []
    for variety, detail in data.get('variety_details', {}).items():
        grades = []
        for grade, weight in detail.get('grades_weights', {}).items():
            if weight > 0:
                grade_id = detail['grade_value_to_id'][grade]
                grades.append({
                    "id": grade_id,
                    "weight": weight
                })
        blooming_date = detail.get('blooming_date', None)
        if not blooming_date:
            continue
        blooming_date_thai = datetime.strptime(blooming_date, '%Y-%m-%d')
        if blooming_date_thai.year > datetime.now().year + 500:
            blooming_date_thai = thai_to_gregorian(blooming_date_thai)
        varieties.append({
            "id": detail['id'],
            "grades": grades,
            "flower_blooming_day": blooming_date_thai.replace(tzinfo=timezone(tz_offset)).timestamp(),
            "name": detail.get('variety_name', None),
        })

    packing_house = {
        "packing_house_id": data.get("packing_house_id"),
        "weight": data['total_weight']
    } if data.get("packing_house_id") else None

    api_data = {
        "batch_name": data.get("harvest_lot_name"),
        "status": data.get("status", "draft"),
        "latitude": data.get("position_latitude", 13.8),
        "longitude": data.get("position_longitude", 100.5),
        "harvest_images": data.get("durian_image_ids", []),
        "cutting_day": harvest_date_thai.replace(tzinfo=timezone(tz_offset)).timestamp(),
        "cutter": cutter,
        "cutter_vehicle": cutter_vehicle,
        "farm": farm,
        "packing_house": packing_house,
        "varieties": varieties
    }

    headers = {
        'Content-Type': 'application/json',
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }
    logger.info(f"Sending durian draft: {api_data}")

    response = REQUEST_UTIL.post(f"{DURIAN_API_URL}/durian/events/harvesting", headers=headers, json=api_data)
    logger.info(f"Send durian draft response: {json.dumps(response.json(), indent=4)}")
    return response.json()

def update_durian_event_harvest(user_id: str, event_id: UUID, data: dict):
    """Update durian harvest information as draft."""
    api_data = data
    headers = {
        'Content-Type': 'application/json',
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }
    logger.info(f"Updating durian event data: {api_data}")

    response = REQUEST_UTIL.patch(f"{DURIAN_API_URL}/durian/events/harvesting/{event_id}", headers=headers, json=api_data)
    logger.info(f"Update durian event response: {json.dumps(response.json(), indent=4)}")
    return response.json()

def get_durian_event_harvest(user_id: str, event_id: UUID) -> dict | None:
    """Get durian harvest information based on event ID."""
    url = f'{DURIAN_API_URL}/durian/events/harvesting/{event_id}'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    try:
        response = REQUEST_UTIL.get(url=url, headers=headers)
    except Exception as e:
        logger.error(f"Error getting durian event: {str(e)}")
        return None
    return response.json().get('data')

def get_farm_info(farm_id: str, user_id: str):
    """Get farm information based on farm ID. This API only return ID, Plots, Area"""

    url = f'{DURIAN_API_URL}/durian/farm/{farm_id}/info'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', {})

def get_list_farms(user_id: str):
    """Get list of farms based on user access token."""
    url = f'{DURIAN_API_URL}/durian/farm/suggestion/?list_all_farm=true'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', [])

def get_list_farm_plots(farm_id: str, user_id: str):
    """Get list of farm plots based on farm ID and user access token."""
    url = f'{DURIAN_API_URL}/durian/farm/{farm_id}/plots'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', [])

def get_list_packing_houses(user_id: str):
    """Get list of packing houses based on user access token."""
    url = f'{DURIAN_API_URL}/durian/packing-houses'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', [])

def get_product_info(product_id: str, user_id: str):
    """Get product information based on product ID and user access token."""
    url = f'{DURIAN_API_URL}/durian/product/{product_id}/info'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', {})

def create_farm_plot(user_id: str, farm_id: str, plot_name: str, gap: str | None, area: float, plot_id: str | None):
    """Create a new farm plot."""
    url = f'{DURIAN_API_URL}/durian/farm/{farm_id}/plot'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'Content-Type': 'application/json',
        'x-user-id': user_id
    }
    payload = {
        "name": plot_name,
        "gap": gap,
        "area": area,
        "plot_id": plot_id
    }

    response = REQUEST_UTIL.post(url=url, headers=headers, json=payload)
    return response.json().get('data', {})

def get_list_cutters(user_id: str):
    """Get list of cutters based on user access token."""
    url = f'{DURIAN_API_URL}/search/cutters?q=%22%22'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', [])

def check_existing_plot(farm_id: str, plot_name: str, user_id: str):
    """Check if a plot exists in a farm."""
    url = f'{DURIAN_API_URL}/durian/farm/{farm_id}/check-plot-name?name={plot_name}'
    headers = {
        'x-api-key': CONTROLLER_API_KEY,
        'x-user-id': user_id
    }

    response = REQUEST_UTIL.get(url=url, headers=headers)
    return response.json().get('data', {})