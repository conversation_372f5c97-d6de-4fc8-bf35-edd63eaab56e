import json
from linebot.v3.messaging import (
    Configuration,
    ApiClient,
    MessagingApi,
    MessagingApiBlob,
    ReplyMessageRequest
)
from linebot.v3.messaging.models.message import Message
from utils.request_util import REQUEST_UTIL
from utils.logger import logger
from utils.config import LINE_API_URL, LINE_CHANNEL_ACCESS_TOKEN
from utils.memory_cache import MemoryCache

# LINE v3 SDK setup
configuration = Configuration(access_token=LINE_CHANNEL_ACCESS_TOKEN)

def send_reply_message(reply_token, messages):
    """Send reply message(s) to LINE."""
    try:
        with ApiClient(configuration) as api_client:
            line_bot_api = MessagingApi(api_client)
            if not isinstance(messages, list):
                messages = [messages]
            for index, msg in enumerate(messages):
                if isinstance(msg, dict):
                    messages[index] = Message.from_dict(msg)
            line_bot_api.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token=reply_token,
                    messages=messages
                )
            )
    except Exception as e:
        logger.error(f"Failed to send reply: {str(e)}")
        raise

def get_message_binary_content(message_id):
    """Get binary content of a message from LINE.
    
    Args:
        message_id (str): The message ID of the message
        
    Returns:
        bytes: The binary content of the message
        
    Raises:
        Exception: If there's an error retrieving the content
    """
    try:
        with ApiClient(configuration) as api_client:
            blob_api = MessagingApiBlob(api_client)
            message_content = blob_api.get_message_content(message_id=message_id)
            return message_content
    except Exception as e:
        logger.error(f"Failed to get message content: {str(e)}")
        raise

def send_flex_message(line_user_id, flex_message):
    # Create payload
    if isinstance(flex_message, dict):
        payload = {
            "to": line_user_id,
            "messages": [
                {
                    "type": "flex",
                    "altText": "Flex message",
                    "contents": flex_message
                }
            ]
        }
    elif isinstance(flex_message, list):
        
        payload = {
            "to": line_user_id,
            "messages": [
                {
                    "type": "flex",
                    "altText": "Flex message",
                    "contents": msg
                } for msg in flex_message
            ]
        }
    else:
        raise ValueError("Flex message must be a dict or list")

    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {LINE_CHANNEL_ACCESS_TOKEN}"
    }
    
    # Send Request
    response = REQUEST_UTIL.post(LINE_API_URL, headers=headers, data=json.dumps(payload))

def send_line_message(line_user_id: str = None, reply_token: str = None, message: dict = None):
    """Send message to LINE based on its type.
    
    Args:
        user_id (str): The LINE user ID
        reply_token (str): The reply token (used for reply messages)
        message (dict or list): The message to send. Should contain 'type' and 'message' fields,
            or be a list of such messages.
    """
    if not message:
        raise ValueError("Message cannot be None")
    if not line_user_id and not reply_token:
        raise ValueError("Either line_user_id or reply_token must be provided")
    if not isinstance(message, (dict, list)):
        raise ValueError("Message must be a dict or list")
    try:
        if isinstance(message, list):
            # Group messages by type
            reply_messages = []
            flex_messages = []
            
            for msg in message:
                if msg['type'] == 'reply':
                    if isinstance(msg['message'], list):
                        reply_messages.extend(msg['message'])
                    else:
                        reply_messages.append(msg['message'])
                elif msg['type'] == 'flex':
                    flex_messages.append(msg['message'][0] if isinstance(msg['message'], list) else msg['message'])
            
            # Send all reply messages together (reply_token can only be used once)
            if reply_messages:
                if not reply_token:
                    raise ValueError("reply_token is required when sending reply messages")
                send_reply_message(reply_token, reply_messages)
            
            # Send each flex message
            if flex_messages and not line_user_id:
                raise ValueError("line_user_id is required when sending flex messages")
            for flex_msg in flex_messages:
                send_flex_message(line_user_id, flex_msg)

            # Cache the last message which has just been sent
            if len(flex_messages) == 0:
                cache_last_message(line_user_id=line_user_id, message=reply_messages[-1], type="reply")
            else:
                cache_last_message(line_user_id=line_user_id, message=flex_messages[-1], type="flex")

        else:
            # Single message
            if message['type'] == 'reply':
                if not reply_token:
                    raise ValueError("reply_token is required when sending reply messages")
                send_reply_message(reply_token, message['message'])
            elif message['type'] == 'flex':
                if not line_user_id:
                    raise ValueError("line_user_id is required when sending flex messages")
                send_flex_message(line_user_id, message['message'])
            else:
                raise ValueError(f"Unknown message type: {message['type']}")

            # Cache the last message which has just been sent
            cache_last_message(
                line_user_id=line_user_id,
                message=message['message'][-1] if isinstance(message['message'], list) else message["message"],
                type=message['type']
            )

    except Exception as e:
        logger.error(f"Failed to send LINE message: {str(e)}")
        raise

def cache_last_message(line_user_id: str, message: Message | dict, type: str):
    cache_msg = {
        "type": type,
        "message": message if isinstance(message, dict) else Message.to_dict(message)
    }
    MemoryCache.set(line_user_id, 'last_sent_msg', cache_msg)