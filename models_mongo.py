import uuid
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.errors import CollectionInvalid
from utils.config import MONGODB_URI, MONGODB_DB_NAME
from utils.logger import logger
from datetime import datetime

class MongoDB:
    _instance = None
    def __new__(cls):
        if cls._instance is None:
            logger.info("Connecting to MongoDB...")
            cls._instance = super(MongoDB, cls).__new__(cls)
            cls._instance.client = MongoClient(MONGODB_URI)
            cls._instance.db = cls._instance.client[MONGODB_DB_NAME]

            if 'line_durian_records' not in cls._instance.db.list_collection_names():
                try:
                    cls._instance.db.create_collection('line_durian_records')
                    logger.info("Collection 'line_durian_records' created.")
                except CollectionInvalid:
                    logger.info("Collection 'line_durian_records' already exists.")
            else:
                logger.info("Collection 'line_durian_records' already exists.")

            if 'line_users' not in cls._instance.db.list_collection_names():
                try:
                    cls._instance.db.create_collection('line_users')
                    logger.info("Collection 'line_users' created.")
                except CollectionInvalid:
                    logger.info("Collection 'line_users' already exists.")
            else:
                logger.info("Collection 'line_users' already exists.")
        return cls._instance

    def get_db(self) -> Database:
        return self.db

class DurianRecord:
    def __init__(self, event_id, harvest_lot_name, farm_id, selected_plots, 
                 harvest_date, created_at, durian_image_ids,
                 variety_details, position_latitude, position_longitude,
                 cutter_name, cutter_registration, cutter_registration_number=None,
                 cutter_image_id=None, vehicle_image_id=None,
                 vehicle_registration_number=None, vehicle_registration_province=None,
                 packing_house=None, status='draft'):
        self.id = str(uuid.uuid4())
        self.event_id = event_id
        self.harvest_lot_name = harvest_lot_name
        self.farm_id = farm_id
        self.selected_plots = selected_plots
        self.harvest_date = harvest_date
        self.created_at = created_at or datetime.now()
        self.durian_image_ids = durian_image_ids
        self.variety_details = variety_details
        self.position_latitude = position_latitude
        self.position_longitude = position_longitude
        self.cutter_name = cutter_name
        self.cutter_registration = cutter_registration
        self.cutter_registration_number = cutter_registration_number
        self.cutter_image_id = cutter_image_id
        self.vehicle_image_id = vehicle_image_id
        self.vehicle_registration_number = vehicle_registration_number
        self.vehicle_registration_province = vehicle_registration_province
        self.packing_house = packing_house
        self.status = status

    def to_dict(self):
        return self.__dict__

    def __repr__(self):
        return f"<DurianRecord(harvest_lot={self.harvest_lot_name}, farm={self.farm_id}, status={self.status})>"

class User:
    def __init__(self, line_user_id, user_id=None, language=None):
        self.line_user_id: str = line_user_id
        self.user_id: str = user_id
        self.language: str = language

    def to_dict(self):
        return self.__dict__

    def __repr__(self):
        return f"<User(line_user_id={self.line_user_id}, access_token={self.access_token}, language={self.language})>"
