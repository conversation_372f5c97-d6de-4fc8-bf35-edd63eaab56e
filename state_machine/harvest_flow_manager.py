"""
Harvest Flow Manager for handling user interactions with the harvest state machine.
"""

# Removed unused imports
from .harvest_state_machine import HarvestStateMachine
from utils.logger import logger
from utils.memory_cache import MemoryCache
from utils.line_utils import create_harvest_question_response
from service.line_service import send_line_message
from utils.durian_info import DURIAN_INFO
from service.ptp_controller_service import (
    get_list_farms, get_list_plots, get_list_cutters, get_list_packing_houses,
    send_durian_event_harvest, get_farmer_cutter_info
)
from service.database_mongo_service import DATABASE_SERVICE_MONGO
from utils.language_mapping import get_displayed_texts
from linebot.v3.messaging import TextMessage


class HarvestFlowManager:
    """Manages harvest flow using state machine"""

    def __init__(self, line_user_id: str, language: str = 'thailand'):
        self.line_user_id = line_user_id
        self.language = language
        self.state_machine = HarvestStateMachine(line_user_id, language)
        self.database = DATABASE_SERVICE_MONGO()
        self.current_reply_token = None  # Store current reply token for callbacks

        # Connect state machine callbacks to flow manager methods
        self._setup_state_machine_callbacks()

    def _setup_state_machine_callbacks(self):
        """Setup state machine callbacks to use flow manager methods"""
        # Override state machine callback methods to use flow manager methods
        self.state_machine._send_next_question_after_harvest_lot_name = self._send_next_question_after_harvest_lot_name
        self.state_machine._send_plot_selection_question = self._send_plot_selection_question_callback
        self.state_machine._send_plot_certificate_request = self._send_plot_certificate_request
        self.state_machine._send_next_question_after_grade_weight_confirmation = self._send_next_question_after_grade_weight_confirmation
        self.state_machine._send_packing_house_selection = self._send_packing_house_selection_callback
        self.state_machine._create_harvest_event = self._create_harvest_event
        self.state_machine._send_final_confirmation = self._send_final_confirmation_callback

    def _send_next_question_after_harvest_lot_name(self):
        """Send appropriate question after harvest lot name is provided"""
        if self.current_reply_token:
            if self.state_machine.user_role == 'cutter':
                self._send_farm_selection_question(self.current_reply_token)
            else:  # farmer
                self._send_plot_selection_question(self.current_reply_token)

    def _send_plot_selection_question_callback(self):
        """Callback wrapper for plot selection question"""
        if self.current_reply_token:
            self._send_plot_selection_question(self.current_reply_token)

    def _send_plot_certificate_request(self):
        """Send plot certificate image request"""
        if self.current_reply_token:
            plot_cert_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_PLOT_CERTIFICATE_IMAGE'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=self.current_reply_token,
                message=plot_cert_prompt
            )

    def _send_next_question_after_grade_weight_confirmation(self):
        """Send next question after grade weight confirmation"""
        if self.current_reply_token:
            if self.state_machine.has_more_varieties():
                # Send additional varieties question
                more_varieties_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_ADDITIONAL_VARIETIES'
                )
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=self.current_reply_token,
                    message=more_varieties_prompt
                )
            else:
                # Send durian photo request
                durian_photo_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_DURIAN_PHOTO',
                    extra_context={'allow_skip': True}
                )
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=self.current_reply_token,
                    message=durian_photo_prompt
                )

    def _send_packing_house_selection_callback(self):
        """Callback wrapper for packing house selection"""
        if self.current_reply_token:
            # This will be implemented to send packing house options
            pass

    def _create_harvest_event(self):
        """Create harvest event in PTP controller"""
        # This will be implemented to create the event
        pass

    def _send_final_confirmation_callback(self):
        """Callback wrapper for final confirmation"""
        if self.current_reply_token:
            # This will be implemented to send final confirmation
            pass

    def start_harvest_flow(self, user_role: str, reply_token: str):
        """Start the harvest flow"""
        try:
            # Initialize state machine
            self.state_machine.user_role = user_role
            self.state_machine.data = {}

            # Clear previous data except language and signed_in
            MemoryCache.delete_all_except(self.line_user_id, ['language', 'signed_in'])

            # Trigger the initial transition
            self.state_machine.collect_harvest_info()

            # Save state
            self.state_machine.save_state()

            # Send initial instruction message
            instruction_msg = create_harvest_question_response(
                self.language,
                'INIT',
                extra_context={'user_role': user_role}
            )

            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=instruction_msg
            )

            # Send first question
            self._send_current_state_question(reply_token)

        except Exception as e:
            logger.error(f"Error starting harvest flow: {str(e)}")
            self._send_error_message(reply_token)

    def process_text_input(self, text: str, reply_token: str):
        """Process text input from user

        Note: The following states do NOT handle text input:
        - AWAITING_HARVEST_DATE (postback only)
        - AWAITING_BLOOMING_DATE_PER_VARIETY (postback only)
        - AWAITING_CUTTER_SELECTION (postback only)
        - AWAITING_PLOT_CERTIFICATE_IMAGE (image only)
        - AWAITING_DURIAN_PHOTO (image only)
        - AWAITING_CUTTER_PHOTO (image only)
        - AWAITING_VEHICLE_PHOTO (image only)
        """
        try:
            # Store reply token for callbacks
            self.current_reply_token = reply_token

            # Load current state
            self.state_machine.load_state()
            current_state = self.state_machine.get_current_state_name()

            logger.info(f"Processing text input '{text}' in state {current_state}")

            # Check if current state should not handle text input
            image_only_states = [
                'AWAITING_PLOT_CERTIFICATE_IMAGE',
                'AWAITING_DURIAN_PHOTO',
                'AWAITING_CUTTER_PHOTO',
                'AWAITING_VEHICLE_PHOTO'
            ]
            postback_only_states = [
                'AWAITING_HARVEST_DATE',
                'AWAITING_BLOOMING_DATE_PER_VARIETY',
                'AWAITING_CUTTER_SELECTION'
            ]

            if current_state in image_only_states:
                raise ValueError(f"State {current_state} only accepts image input, not text")
            elif current_state in postback_only_states:
                raise ValueError(f"State {current_state} only accepts postback input, not text")

            # Process input based on current state
            if current_state == 'AWAITING_HARVEST_LOT_NAME':
                self._process_harvest_lot_name(text, reply_token)
            elif current_state == 'AWAITING_FARM_SELECTION':
                self._process_farm_selection(text, reply_token)
            elif current_state == 'AWAITING_PLOT_SELECTION':
                self._process_plot_selection(text, reply_token)
            elif current_state == 'AWAITING_ADDITIONAL_PLOTS':
                self._process_additional_plots(text, reply_token)
            elif current_state == 'AWAITING_VARIETY_SELECTION':
                self._process_variety_selection(text, reply_token)
            elif current_state == 'AWAITING_GRADE_WEIGHT_PER_VARIETY':
                self._process_grade_weight_selection(text, reply_token)
            elif current_state == 'AWAITING_WEIGHT_PER_VARIETY':
                self._process_weight_input(text, reply_token)
            elif current_state == 'AWAITING_ADDITIONAL_GRADES':
                self._process_additional_grades(text, reply_token)
            elif current_state == 'AWAITING_GRADE_WEIGHT_CONFIRMATION':
                self._process_grade_weight_confirmation(text, reply_token)
            elif current_state == 'AWAITING_ADDITIONAL_VARIETIES':
                self._process_additional_varieties(text, reply_token)
            elif current_state == 'AWAITING_ADD_VARIETY_NAME':
                self._process_add_variety_name(text, reply_token)
            elif current_state == 'AWAITING_CUTTER_NAME':
                self._process_cutter_name(text, reply_token)
            elif current_state == 'AWAITING_CUTTER_REGISTRATION':
                self._process_cutter_registration(text, reply_token)
            elif current_state == 'AWAITING_CUTTER_REGISTRATION_NUMBER':
                self._process_cutter_registration_number(text, reply_token)
            elif current_state == 'AWAITING_VEHICLE_REGISTRATION_NUMBER':
                self._process_vehicle_registration_number(text, reply_token)
            elif current_state == 'AWAITING_VEHICLE_REGISTRATION_PROVINCE':
                self._process_vehicle_registration_province(text, reply_token)
            elif current_state == 'AWAITING_SEND_OR_DRAFT':
                self._process_send_or_draft(text, reply_token)
            elif current_state == 'AWAITING_PACKING_HOUSE':
                self._process_packing_house_selection(text, reply_token)
            elif current_state == 'AWAITING_FINAL_CONFIRMATION':
                self._process_final_confirmation(text, reply_token)
            else:
                # Invalid input for current state
                self._send_invalid_input_message(reply_token)

        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error(f"Error processing text input: {str(e)}")
            self._handle_text_processing_exception(e, reply_token)

    def process_postback_input(self, data: str, reply_token: str):
        """Process postback input from user

        Handles these states:
        - AWAITING_HARVEST_DATE (date picker postback)
        - AWAITING_BLOOMING_DATE_PER_VARIETY (date picker postback)
        - AWAITING_CUTTER_SELECTION (cutter selection buttons)
        """
        try:
            # Store reply token for callbacks
            self.current_reply_token = reply_token

            # Load current state
            self.state_machine.load_state()
            current_state = self.state_machine.get_current_state_name()

            logger.info(f"Processing postback '{data}' in state {current_state}")

            # Process postback based on current state
            if current_state == 'AWAITING_HARVEST_DATE':
                self._process_harvest_date_postback(data, reply_token)
            elif current_state == 'AWAITING_BLOOMING_DATE_PER_VARIETY':
                self._process_blooming_date_postback(data, reply_token)
            elif current_state == 'AWAITING_CUTTER_SELECTION':
                self._process_cutter_selection_postback(data, reply_token)
            else:
                # Invalid postback for current state
                self._send_invalid_input_message(reply_token)

        except Exception as e:
            logger.error(f"Error processing postback input: {str(e)}")
            self._send_error_message(reply_token)

    def process_image_input(self, image_content: bytes, reply_token: str):
        """Process image input from user

        Handles these states:
        - AWAITING_PLOT_CERTIFICATE_IMAGE (plot certificate upload)
        - AWAITING_DURIAN_PHOTO (durian photo upload)
        - AWAITING_CUTTER_PHOTO (cutter photo upload)
        - AWAITING_VEHICLE_PHOTO (vehicle photo upload)
        """
        try:
            # Store reply token for callbacks
            self.current_reply_token = reply_token

            # Load current state
            self.state_machine.load_state()
            current_state = self.state_machine.get_current_state_name()

            logger.info(f"Processing image input in state {current_state}")

            # Process image based on current state
            if current_state == 'AWAITING_PLOT_CERTIFICATE_IMAGE':
                self._process_plot_certificate_image(image_content, reply_token)
            elif current_state == 'AWAITING_DURIAN_PHOTO':
                self._process_durian_photo(image_content, reply_token)
            elif current_state == 'AWAITING_CUTTER_PHOTO':
                self._process_cutter_photo(image_content, reply_token)
            elif current_state == 'AWAITING_VEHICLE_PHOTO':
                self._process_vehicle_photo(image_content, reply_token)
            else:
                # Invalid image for current state
                self._send_invalid_input_message(reply_token)

        except Exception as e:
            logger.error(f"Error processing image input: {str(e)}")
            self._send_error_message(reply_token)

    def _process_harvest_lot_name(self, text: str, reply_token: str):
        """Process harvest lot name input"""
        if not text.strip():
            raise ValueError("Harvest lot name should not be blank")

        if len(text.strip()) > 100:
            raise ValueError("Harvest lot name too long")

        self.state_machine.data['harvest_lot_name'] = text.strip()

        # Trigger transition based on user role
        if self.state_machine.user_role == 'cutter':
            self.state_machine.harvest_lot_name_provided()
            self._send_farm_selection_question(reply_token)
        else:  # farmer
            self.state_machine.harvest_lot_name_provided()
            self._send_plot_selection_question(reply_token)

        self.state_machine.save_state()

    def _send_farm_selection_question(self, reply_token: str):
        """Send farm selection question for cutters"""
        ptp_user_id = self.database.get_line_user(query={'line_user_id': self.line_user_id}).user_id
        list_farms = get_list_farms(ptp_user_id)

        displayed_farms = []
        mapping_farm_name_to_farm_object = {}

        for farm in list_farms:
            farm_name = farm['name']
            if farm_name in displayed_farms:
                farm_name = f"{farm_name} - {farm['farmer_name']}"
            displayed_farms.append(farm_name)
            mapping_farm_name_to_farm_object[farm_name] = farm

        # Save mapping for later use
        MemoryCache.set(self.line_user_id, 'mapping_farm_name_to_farm_object', mapping_farm_name_to_farm_object)

        farm_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_FARM_SELECTION',
            extra_context={'farms': displayed_farms}
        )

        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=farm_prompt
        )

    def _send_plot_selection_question(self, reply_token: str):
        """Send plot selection question"""
        ptp_user_id = self.database.get_line_user(query={'line_user_id': self.line_user_id}).user_id

        if self.state_machine.user_role == 'cutter':
            # Get farm_id from selected farm
            mapping_farm_name_to_farm_object = MemoryCache.get(self.line_user_id, 'mapping_farm_name_to_farm_object')
            selected_farm = self.state_machine.data.get('selected_farm')
            farm_object = mapping_farm_name_to_farm_object.get(selected_farm)
            farm_id = farm_object['id']
            list_plots = get_list_plots(ptp_user_id, farm_id)
        else:  # farmer
            list_plots = get_list_plots(ptp_user_id)

        list_plot_names = [plot['name'] for plot in list_plots]
        MemoryCache.set(self.line_user_id, 'list_plot_names', list_plot_names)

        plot_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_PLOT_SELECTION',
            extra_context={
                'plots': list_plot_names,
                'allow_add_new_plot': True
            }
        )

        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=plot_prompt
        )

    def _send_current_state_question(self, reply_token: str):
        """Send question for current state"""
        current_state = self.state_machine.get_current_state_name()

        if current_state == 'AWAITING_HARVEST_LOT_NAME':
            prompt = create_harvest_question_response(self.language, current_state)
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=prompt
            )

    def _send_error_message(self, reply_token: str):
        """Send error message to user"""
        error_msg = get_displayed_texts("Error occurred", self.language)[0]
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message={"type": "reply", "message": TextMessage(text=error_msg)}
        )

    def _send_invalid_input_message(self, reply_token: str):
        """Send invalid input message to user"""
        invalid_msg = get_displayed_texts("Invalid input", self.language)[0]
        last_sent_msg = MemoryCache.get(self.line_user_id, "last_sent_msg")

        messages = [{"type": "reply", "message": TextMessage(text=invalid_msg)}]
        if last_sent_msg:
            messages.append(last_sent_msg)

        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=messages
        )

    def _handle_text_processing_exception(self, e: Exception, reply_token: str):
        """Handle exceptions during text processing with specific error messages"""
        from utils.exception import CharacterLimitExceededError, DataExistsError, SpecialCharacterError

        current_state = self.state_machine.get_current_state_name()

        if isinstance(e, (ValueError, TypeError)):
            msg_invalid_input = get_displayed_texts("Invalid input", self.language)[0]
            sent_invalid_input_msg = {
                "type": "reply",
                "message": TextMessage(text=msg_invalid_input)
            }
            last_sent_msg = MemoryCache.get(self.line_user_id, "last_sent_msg")
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=[sent_invalid_input_msg, last_sent_msg] if last_sent_msg else sent_invalid_input_msg
            )

        elif isinstance(e, CharacterLimitExceededError):
            # Send character limit exceeded error message
            msg_error = get_displayed_texts("Character limit exceeded", self.language)[0]
            if current_state == 'AWAITING_VEHICLE_REGISTRATION_NUMBER':
                msg_error = msg_error.format(char_limit=20)
            else:
                msg_error = msg_error.format(char_limit=100)
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "reply", "message": msg}
            )

        elif isinstance(e, DataExistsError):
            # Send data exists error message
            msg_error = get_displayed_texts("Data already exists", self.language)[0]
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "reply", "message": msg}
            )

        elif isinstance(e, SpecialCharacterError):
            # Send special character error message
            msg_error = get_displayed_texts("Input contains special characters", self.language)[0]
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "reply", "message": msg}
            )

        else:
            # Send generic error message
            msg_error = get_displayed_texts("Error processing text message", self.language)[0]
            msg = TextMessage(text=msg_error)
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "reply", "message": msg}
            )

    def _process_farm_selection(self, text: str, reply_token: str):
        """Process farm selection input"""
        mapping_farm_name_to_farm_object = MemoryCache.get(self.line_user_id, 'mapping_farm_name_to_farm_object')

        if text not in mapping_farm_name_to_farm_object:
            raise ValueError(f"Invalid farm selection: {text}")

        self.state_machine.data['selected_farm'] = text
        farm_object = mapping_farm_name_to_farm_object[text]
        self.state_machine.data['farm_id'] = farm_object['id']

        # Transition to plot selection
        self.state_machine.farm_selected()
        self._send_plot_selection_question(reply_token)
        self.state_machine.save_state()

    def _process_plot_selection(self, text: str, reply_token: str):
        """Process plot selection input"""
        list_plot_names = MemoryCache.get(self.line_user_id, 'list_plot_names') or []

        if text not in list_plot_names and text not in ["Add new plot", "เพิ่มแปลงใหม่"]:
            raise ValueError(f"Invalid plot selection: {text}")

        if text in ["Add new plot", "เพิ่มแปลงใหม่"]:
            # Handle add new plot logic here
            # For now, just raise an error as this needs more implementation
            raise ValueError("Add new plot not implemented yet")
        else:
            self.state_machine.data['selected_plot'] = text

            # Transition to plot certificate image
            self.state_machine.plot_selected()

            # Send plot certificate image prompt
            plot_cert_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_PLOT_CERTIFICATE_IMAGE'
            )

            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=plot_cert_prompt
            )

        self.state_machine.save_state()

    def _process_additional_plots(self, text: str, reply_token: str):
        """Process additional plots input"""
        if text in ["Yes, add more plots", "ใช่เพิ่มรายละเอียดแปลง"]:
            self.state_machine.additional_plots_yes()
            self._send_plot_selection_question(reply_token)
        elif text in ["No", "ไม่มี"]:
            self.state_machine.additional_plots_no()
            # Send harvest date prompt
            harvest_date_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_HARVEST_DATE'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=harvest_date_prompt
            )
        else:
            raise ValueError(f"Invalid additional plots option: {text}")

        self.state_machine.save_state()

    def _process_variety_selection(self, text: str, reply_token: str):
        """Process variety selection input"""
        varieties = DURIAN_INFO.get_varieties()
        variety_names = [DURIAN_INFO.mapping_variety_value_to_text(v, self.language) for v in varieties]

        if text not in variety_names and text not in ["Other", "อื่นๆ"]:
            raise ValueError(f"Invalid variety selection: {text}")

        if text in ["Other", "อื่นๆ"]:
            current_variety = "other"
            # For "other" variety, ask for variety name first
            self.state_machine.data['current_variety'] = current_variety

            # Initialize variety details
            if 'variety_details' not in self.state_machine.data:
                self.state_machine.data['variety_details'] = {}
            if current_variety not in self.state_machine.data['variety_details']:
                self.state_machine.data['variety_details'][current_variety] = {
                    'grades_weights': {},
                    'total_weight': 0
                }

            # Transition to add variety name
            self.state_machine.variety_selected()

            # Send add variety name prompt
            add_variety_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_ADD_VARIETY_NAME'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=add_variety_prompt
            )
        else:
            # Find variety value from text
            current_variety = None
            for variety in varieties:
                if DURIAN_INFO.mapping_variety_value_to_text(variety, self.language) == text:
                    current_variety = variety
                    break

            if not current_variety:
                raise ValueError(f"Could not find variety for: {text}")

            self.state_machine.data['current_variety'] = current_variety

            # Initialize variety details if not exists
            if 'variety_details' not in self.state_machine.data:
                self.state_machine.data['variety_details'] = {}
            if current_variety not in self.state_machine.data['variety_details']:
                self.state_machine.data['variety_details'][current_variety] = {
                    'grades_weights': {},
                    'total_weight': 0
                }

            # Add to selected varieties
            if 'selected_varieties' not in self.state_machine.data:
                self.state_machine.data['selected_varieties'] = []
            if current_variety not in self.state_machine.data['selected_varieties']:
                self.state_machine.data['selected_varieties'].append(current_variety)

            # Transition to blooming date selection
            self.state_machine.variety_selected()

            # Send blooming date prompt
            blooming_date_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_BLOOMING_DATE_PER_VARIETY',
                extra_context={'variety_name': DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language)}
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=blooming_date_prompt
            )

        self.state_machine.save_state()

    def _process_grade_weight_selection(self, text: str, reply_token: str):
        """Process grade weight selection input"""
        current_available_grades = MemoryCache.get(self.line_user_id, 'current_available_grades') or []

        if text not in current_available_grades:
            raise ValueError(f"Invalid grade selection: {text}")

        current_variety = self.state_machine.data.get('current_variety')
        grade_value = DURIAN_INFO.get_grade_value(current_variety, text)

        self.state_machine.data['current_grade'] = grade_value
        self.state_machine.data['current_grade_label'] = text

        # Transition to weight input
        self.state_machine.grade_weight_provided()

        # Send weight input prompt
        weight_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_WEIGHT_PER_VARIETY',
            extra_context={'grade': text}
        )

        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=weight_prompt
        )

        self.state_machine.save_state()

    def _process_weight_input(self, text: str, reply_token: str):
        """Process weight input"""
        try:
            weight = float(text.strip())
            if weight <= 0:
                raise ValueError("Weight must be positive")
        except ValueError:
            raise ValueError(f"Invalid weight format: {text}")

        current_variety = self.state_machine.data.get('current_variety')
        current_grade = self.state_machine.data.get('current_grade')

        # Store weight
        self.state_machine.data['variety_details'][current_variety]['grades_weights'][current_grade] = weight

        # Update total weight
        total_weight = sum(self.state_machine.data['variety_details'][current_variety]['grades_weights'].values())
        self.state_machine.data['variety_details'][current_variety]['total_weight'] = total_weight

        # Check if there are more grades to add
        selected_grades = self.state_machine.data['variety_details'][current_variety]['grades_weights'].keys()
        current_available_grades = MemoryCache.get(self.line_user_id, 'current_available_grades') or []

        if len(selected_grades) == len(current_available_grades):
            # All grades processed, move to confirmation
            self.state_machine.weight_provided()
            self._send_grade_weight_confirmation(reply_token)
        else:
            # More grades available, ask if user wants to add more
            self.state_machine.weight_provided()

            additional_grades_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_ADDITIONAL_GRADES'
            )

            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=additional_grades_prompt
            )

        self.state_machine.save_state()

    def _process_additional_grades(self, text: str, reply_token: str):
        """Process additional grades input"""
        if text in ["Yes, add more grades", "ใช่เพิ่มรายละเอียดเกรด"]:
            self.state_machine.additional_grades_yes()

            # Get remaining grades
            current_variety = self.state_machine.data.get('current_variety')
            current_available_grades = MemoryCache.get(self.line_user_id, 'current_available_grades') or []
            selected_grades = self.state_machine.data['variety_details'][current_variety]['grades_weights'].keys()

            remain_grades = []
            for grade_label in current_available_grades:
                grade_value = DURIAN_INFO.get_grade_value(current_variety, grade_label)
                if grade_value not in selected_grades:
                    remain_grades.append(grade_label)

            if current_variety == "other":
                variety_name = self.state_machine.data['variety_details']['other'].get('variety_name', 'Other')
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language)

            grade_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_GRADE_WEIGHT_PER_VARIETY',
                extra_context={
                    'current_variety': variety_name,
                    'grades': remain_grades
                }
            )

            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=grade_prompt
            )

        elif text in ["No", "ไม่มี"]:
            self.state_machine.additional_grades_no()
            self._send_grade_weight_confirmation(reply_token)
        else:
            raise ValueError(f"Invalid additional grades option: {text}")

        self.state_machine.save_state()

    def _send_grade_weight_confirmation(self, reply_token: str):
        """Send grade weight confirmation"""
        current_variety = self.state_machine.data.get('current_variety')

        if current_variety == "other":
            variety_name = self.state_machine.data['variety_details']['other'].get('variety_name', 'Other')
        else:
            variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language)

        grades_weights = self.state_machine.data['variety_details'][current_variety]['grades_weights']
        total_weight = self.state_machine.data['variety_details'][current_variety]['total_weight']

        confirm_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_GRADE_WEIGHT_CONFIRMATION',
            extra_context={
                'current_variety': current_variety,
                'variety_name': variety_name,
                'grades_weights': grades_weights,
                'total_weight': total_weight
            }
        )

        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=confirm_prompt
        )

    def _process_grade_weight_confirmation(self, text: str, reply_token: str):
        """Process grade weight confirmation"""
        if text in ["Yes", "ใช่"]:
            # Check if there are more varieties to add
            selected_varieties = self.state_machine.data.get('selected_varieties', [])

            if len(selected_varieties) < len(DURIAN_INFO.get_varieties()):
                self.state_machine.grade_weight_confirmed()

                # Ask for additional varieties
                additional_varieties_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_ADDITIONAL_VARIETIES'
                )

                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=additional_varieties_prompt
                )
            else:
                # All varieties processed, move to durian photo
                self.state_machine.grade_weight_confirmed()

                durian_photo_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_DURIAN_PHOTO',
                    extra_context={'allow_skip': True}
                )

                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=durian_photo_prompt
                )

        elif text in ["No", "ไม่"]:
            # Go back to grade weight selection for current variety
            current_variety = self.state_machine.data.get('current_variety')

            # Reset grades_weights for current variety
            self.state_machine.data['variety_details'][current_variety]['grades_weights'] = {}
            self.state_machine.data['variety_details'][current_variety]['total_weight'] = 0

            # Go back to grade selection
            current_available_grades = DURIAN_INFO.get_grade_label_by_variety_value(current_variety, self.language) or []

            if current_variety == "other":
                variety_name = self.state_machine.data['variety_details']['other'].get('variety_name', 'Other')
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language)

            grade_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_GRADE_WEIGHT_PER_VARIETY',
                extra_context={
                    'current_variety': variety_name,
                    'grades': current_available_grades
                }
            )

            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=grade_prompt
            )
        else:
            raise ValueError(f"Invalid confirmation option: {text}")

        self.state_machine.save_state()

    def _process_add_variety_name(self, text: str, reply_token: str):
        """Process add variety name input for 'other' variety"""
        # Validate input
        if len(text) > 100:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters
        import re
        if re.search(r'[^\w\s\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save variety name
        self.state_machine.data['variety_details']['other']['variety_name'] = text.strip()

        # Add to selected varieties
        if 'selected_varieties' not in self.state_machine.data:
            self.state_machine.data['selected_varieties'] = []
        if 'other' not in self.state_machine.data['selected_varieties']:
            self.state_machine.data['selected_varieties'].append('other')

        # Transition to blooming date selection
        self.state_machine.variety_name_provided()

        # Send blooming date prompt
        blooming_date_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_BLOOMING_DATE_PER_VARIETY',
            extra_context={'variety_name': text.strip()}
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=blooming_date_prompt
        )

        self.state_machine.save_state()

    def _process_cutter_name(self, text: str, reply_token: str):
        """Process cutter name input for new cutter"""
        # Validate input
        if len(text) > 100:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters
        import re
        if re.search(r'[^\w\s\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save cutter name
        self.state_machine.data['cutter_name'] = text.strip()

        # Transition to cutter registration question
        self.state_machine.cutter_name_provided()

        # Send cutter registration prompt
        cutter_registration_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_CUTTER_REGISTRATION'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=cutter_registration_prompt
        )

        self.state_machine.save_state()

    def _process_cutter_registration(self, text: str, reply_token: str):
        """Process cutter registration status"""
        if text in ["Yes", "ใช่", "Registered", "ลงทะเบียนแล้ว"]:
            self.state_machine.data['cutter_registration'] = True

            # Ask for registration number
            self.state_machine.cutter_registration_provided()

            registration_number_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_CUTTER_REGISTRATION_NUMBER'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=registration_number_prompt
            )

        elif text in ["No", "ไม่", "Not registered", "ไม่ได้ลงทะเบียน"]:
            self.state_machine.data['cutter_registration'] = False
            self.state_machine.data['cutter_registration_number'] = None

            # Move to cutter photo
            self.state_machine.cutter_registration_provided()

            cutter_photo_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_CUTTER_PHOTO'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=cutter_photo_prompt
            )
        else:
            raise ValueError(f"Invalid cutter registration option: {text}")

        self.state_machine.save_state()

    def _process_cutter_registration_number(self, text: str, reply_token: str):
        """Process cutter registration number"""
        # Validate input
        if len(text) > 50:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters (allow alphanumeric and some special chars)
        import re
        if re.search(r'[^\w\s\-\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save registration number
        self.state_machine.data['cutter_registration_number'] = text.strip()

        # Move to cutter photo
        self.state_machine.cutter_registration_number_provided()

        cutter_photo_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_CUTTER_PHOTO'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=cutter_photo_prompt
        )

        self.state_machine.save_state()

    # Stub methods for remaining processing - to be implemented based on existing logic
    def _process_additional_varieties(self, text: str, reply_token: str):
        """Process additional varieties input"""
        if text in ["Yes, add more varieties", "ใช่เพิ่มรายละเอียดทุเรียน"]:
            self.state_machine.additional_varieties_yes()

            # Send variety selection prompt
            variety_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_VARIETY_SELECTION',
                extra_context={'varieties': DURIAN_INFO.get_variety_labels(self.language)}
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=variety_prompt
            )
        elif text in ["No", "ไม่มี"]:
            self.state_machine.additional_varieties_no()

            # Send durian photo request
            durian_photo_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_DURIAN_PHOTO',
                extra_context={'allow_skip': True}
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=durian_photo_prompt
            )
        else:
            raise ValueError(f"Invalid additional varieties option: {text}")

        self.state_machine.save_state()

    def _process_vehicle_registration_number(self, text: str, reply_token: str):
        """Process vehicle registration number input"""
        # Validate input length (max 20 characters for vehicle registration)
        if len(text) > 20:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters (allow alphanumeric and some special chars for vehicle registration)
        import re
        if re.search(r'[^\w\s\-\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save vehicle registration number
        self.state_machine.data['vehicle_registration_number'] = text.strip()

        # Move to vehicle registration province
        self.state_machine.vehicle_registration_provided()

        # Send vehicle registration province prompt
        vehicle_province_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_VEHICLE_REGISTRATION_PROVINCE'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=vehicle_province_prompt
        )

        self.state_machine.save_state()

    def _process_vehicle_registration_province(self, text: str, reply_token: str):
        """Process vehicle registration province input"""
        # Validate input
        if len(text) > 100:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters
        import re
        if re.search(r'[^\w\s\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save vehicle registration province
        self.state_machine.data['vehicle_registration_province'] = text.strip()

        # Move to send or draft selection
        self.state_machine.vehicle_province_provided()

        # Send send or draft prompt
        send_or_draft_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_SEND_OR_DRAFT'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=send_or_draft_prompt
        )

        self.state_machine.save_state()

    def _process_send_or_draft(self, text: str, reply_token: str):
        """Process send or draft selection"""
        if text in ["Send", "ส่ง", "Send to packing house", "ส่งไปโรงแพ็ค"]:
            self.state_machine.send_selected()

            # Get packing houses and send selection prompt
            ptp_user_id = self.database.get_line_user(query={'line_user_id': self.line_user_id}).user_id
            packing_houses = get_list_packing_houses(ptp_user_id)

            packing_house_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_PACKING_HOUSE',
                extra_context={'packing_houses': packing_houses}
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=packing_house_prompt
            )

        elif text in ["Save as draft", "บันทึกแบบร่าง", "Draft", "แบบร่าง"]:
            self.state_machine.draft_selected()

            # Complete the flow - save as draft
            completion_msg = create_harvest_question_response(
                self.language,
                'SAVE_AS_DRAFT'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=completion_msg
            )

        else:
            raise ValueError(f"Invalid send or draft option: {text}")

        self.state_machine.save_state()

    def _process_packing_house_selection(self, text: str, reply_token: str):
        """Process packing house selection"""
        # Validate input
        if len(text) > 100:
            from utils.exception import CharacterLimitExceededError
            raise CharacterLimitExceededError("Character limit exceeded")

        # Check for special characters
        import re
        if re.search(r'[^\w\s\u0E00-\u0E7F]', text):
            from utils.exception import SpecialCharacterError
            raise SpecialCharacterError("Input contains special characters")

        # Save packing house selection
        self.state_machine.data['selected_packing_house'] = text.strip()

        # Move to final confirmation
        self.state_machine.packing_house_selected()

        # Send final confirmation prompt
        final_confirmation_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_FINAL_CONFIRMATION',
            extra_context={'harvest_data': self.state_machine.data}
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=final_confirmation_prompt
        )

        self.state_machine.save_state()

    def _process_final_confirmation(self, text: str, reply_token: str):
        """Process final confirmation"""
        if text in ["Confirm", "ยืนยัน", "Confirm and send", "ยืนยันและส่ง"]:
            # Create harvest event in PTP controller
            try:
                # Get user ID for PTP controller
                ptp_user_id = self.database.get_line_user(query={'line_user_id': self.line_user_id}).user_id

                # Prepare harvest data for PTP controller
                harvest_data = self._prepare_harvest_data_for_ptp()

                # Send to PTP controller
                response = send_durian_event_harvest(ptp_user_id, self.state_machine.user_role, harvest_data)

                if response.get('success') or response.get('data'):
                    # Complete the flow successfully
                    self.state_machine.final_confirmation_confirmed()

                    # Send durian info summary
                    self.send_durian_info(reply_token)

                    success_msg = create_harvest_question_response(
                        self.language,
                        'CONFIRM_AND_SEND'
                    )
                    send_line_message(
                        line_user_id=self.line_user_id,
                        reply_token=None,  # Already used reply_token for durian info
                        message=success_msg
                    )
                else:
                    # Handle PTP controller error
                    error_msg = create_harvest_question_response(
                        self.language,
                        'ERROR_SENDING_DATA'
                    )
                    send_line_message(
                        line_user_id=self.line_user_id,
                        reply_token=reply_token,
                        message=error_msg
                    )

            except Exception as e:
                logger.error(f"Error sending harvest data to PTP controller: {str(e)}")
                error_msg = create_harvest_question_response(
                    self.language,
                    'ERROR_SENDING_DATA'
                )
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=error_msg
                )

        elif text in ["Edit", "แก้ไข", "Edit and send", "แก้ไขและส่ง"]:
            # Allow user to edit - go back to previous step
            edit_msg = create_harvest_question_response(
                self.language,
                'EDIT_AND_SEND'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=edit_msg
            )

        else:
            raise ValueError(f"Invalid final confirmation option: {text}")

        self.state_machine.save_state()

    def _prepare_harvest_data_for_ptp(self):
        """Prepare harvest data for PTP controller API"""
        data = self.state_machine.data

        # Calculate total weight from all varieties and grades
        total_weight = 0.0
        for variety_details in data.get('variety_details', {}).values():
            for weight in variety_details.get('grades_weights', {}).values():
                total_weight += float(weight)

        # Prepare variety details with proper IDs and grade mappings
        variety_details_for_api = {}
        for variety, details in data.get('variety_details', {}).items():
            if variety == 'other':
                # For custom varieties, we need to handle differently
                variety_details_for_api[variety] = {
                    'id': None,  # Will be handled by API
                    'variety_name': details.get('variety_name'),
                    'blooming_date': details.get('blooming_date'),
                    'grades_weights': details.get('grades_weights', {}),
                    'grade_value_to_id': {}  # Custom varieties may not have predefined grade IDs
                }
            else:
                # For standard varieties, get the variety ID
                variety_info = DURIAN_INFO.get_variety_by_value(variety)
                variety_details_for_api[variety] = {
                    'id': variety_info.id if variety_info else None,
                    'variety_name': DURIAN_INFO.mapping_variety_value_to_text(variety, self.language),
                    'blooming_date': details.get('blooming_date'),
                    'grades_weights': details.get('grades_weights', {}),
                    'grade_value_to_id': {}  # Will be populated with grade IDs
                }

                # Map grade values to IDs
                for grade_value in details.get('grades_weights', {}).keys():
                    grade_info = DURIAN_INFO.get_grade_by_variety_and_value(variety, grade_value)
                    if grade_info:
                        variety_details_for_api[variety]['grade_value_to_id'][grade_value] = grade_info.id

        return {
            'harvest_lot_name': data.get('harvest_lot_name'),
            'farm_id': data.get('farm_id'),
            'farm_name': data.get('farm_name'),
            'farm_address': data.get('farm_address'),
            'selected_plots': data.get('selected_plots', []),
            'harvest_date': data.get('harvest_date'),
            'variety_details': variety_details_for_api,
            'total_weight': total_weight,
            'durian_image_ids': data.get('durian_image_ids', []),
            'cutter_id': data.get('cutter_id'),
            'cutter_name': data.get('cutter_name'),
            'cutter_registration': data.get('cutter_registration', False),
            'cutter_registration_number': data.get('cutter_registration_number'),
            'cutter_image_id': data.get('cutter_image_id'),
            'cutter_profile_id': data.get('cutter_profile_id'),
            'vehicle_image_id': data.get('vehicle_image_id'),
            'vehicle_registration_number': data.get('vehicle_registration_number'),
            'vehicle_registration_province': data.get('vehicle_registration_province'),
            'packing_house_id': data.get('packing_house_id'),
            'status': 'draft',  # Default to draft
            'position_latitude': data.get('position_latitude', 13.8),  # Default Bangkok coordinates
            'position_longitude': data.get('position_longitude', 100.5)
        }

    def _process_harvest_date_postback(self, data: str, reply_token: str):
        """Process harvest date postback"""
        from urllib.parse import parse_qsl
        from datetime import datetime, timezone, timedelta
        from utils.time_util import thai_to_gregorian, format_date
        from utils.language_mapping import get_displayed_texts
        from linebot.v3.messaging import TextMessage

        # Parse postback data
        postback_data = dict(parse_qsl(data))

        if postback_data.get('action') == 'harvest_date':
            # Get harvest date from postback params
            harvest_date = postback_data.get('datetime')
            if not harvest_date:
                # Try to get from event.postback.params if available
                # This would need to be passed differently in the actual implementation
                return

            # Send selected date prompt
            harvest_date_info_prompt = {
                "type": "reply",
                "message": TextMessage(text=f"Harvest date: {format_date(harvest_date, self.language)}"
                                        if self.language == 'english'
                                            else f"วันที่เก็บเกี่ยว: {format_date(harvest_date, self.language)}")
            }

            # Validate harvest date
            tz_offset = timedelta(hours=7)
            harvest_date_object = datetime.strptime(harvest_date, '%Y-%m-%dT%H:%M')
            if harvest_date_object.year > datetime.now().year + 500:
                harvest_date_object = thai_to_gregorian(harvest_date_object)
            harvest_date_object = harvest_date_object.replace(tzinfo=timezone(tz_offset))

            if harvest_date_object > datetime.now(tz=timezone(tz_offset)):
                invalid_harvest_date_prompt = {
                    "type": "reply",
                    "message": TextMessage(text=get_displayed_texts("Invalid harvest date", self.language)[0])
                }
                last_sent_msg = MemoryCache.get(self.line_user_id, "last_sent_msg")
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=[harvest_date_info_prompt, invalid_harvest_date_prompt, last_sent_msg]
                )
                return

            # Save harvest date
            self.state_machine.data['harvest_date'] = harvest_date

            # Trigger state machine transition
            self.state_machine.harvest_date_selected()

            # Move to variety selection
            variety_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_VARIETY_SELECTION',
                extra_context={'varieties': DURIAN_INFO.get_variety_labels(self.language)}
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=[harvest_date_info_prompt, variety_prompt]
            )

        self.state_machine.save_state()

    def _process_blooming_date_postback(self, data: str, reply_token: str):
        """Process blooming date postback"""
        from urllib.parse import parse_qsl
        from datetime import datetime, timezone, timedelta
        from utils.time_util import thai_to_gregorian, format_date, convert_to_datetime
        from utils.language_mapping import get_displayed_texts
        from linebot.v3.messaging import TextMessage

        # Parse postback data
        postback_data = dict(parse_qsl(data))

        if postback_data.get('action') == 'blooming_date_per_variety':
            current_variety = self.state_machine.data['selected_varieties'][-1]
            blooming_date = postback_data.get('date')

            if not blooming_date:
                return

            # Send selected date prompt
            blooming_date_info_prompt = {
                "type": "reply",
                "message": TextMessage(text=f"Blooming date: {format_date(blooming_date, self.language)}"
                                         if self.language == 'english'
                                            else f"วันที่ดอกทุเรียนบาน: {format_date(blooming_date, self.language)}")
            }

            # Validate blooming date
            tz_offset = timedelta(hours=7)
            blooming_date_object = datetime.strptime(blooming_date, '%Y-%m-%d')
            if blooming_date_object.year > datetime.now().year + 500:
                blooming_date_object = thai_to_gregorian(blooming_date_object)

            # Check if the blooming date is before the harvest date
            harvest_date_object = datetime.strptime(self.state_machine.data.get('harvest_date'), '%Y-%m-%dT%H:%M')
            if harvest_date_object.year > datetime.now().year + 500:
                harvest_date_object = thai_to_gregorian(harvest_date_object)
            harvest_date_object = harvest_date_object.replace(tzinfo=timezone(tz_offset))

            if blooming_date_object.date() >= harvest_date_object.date():
                invalid_blooming_date_prompt = {
                    "type": "reply",
                    "message": TextMessage(text=get_displayed_texts("Invalid blooming date", self.language)[0])
                }
                last_sent_msg = MemoryCache.get(self.line_user_id, "last_sent_msg")
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=[blooming_date_info_prompt, invalid_blooming_date_prompt, last_sent_msg]
                )
                return

            # Save blooming date
            self.state_machine.data['variety_details'][current_variety]['blooming_date'] = blooming_date

            # Check if the blooming date is before the harvest date based on the flower blooming duration
            current_variety_flower_blooming_duration = DURIAN_INFO.get_variety_by_value(current_variety).flower_blooming_duration
            if blooming_date_object.date() > harvest_date_object.date() - timedelta(days=current_variety_flower_blooming_duration):
                harvest_datetime = convert_to_datetime(self.state_machine.data['harvest_date'])
                initial_date = (harvest_datetime - timedelta(days=DURIAN_INFO.get_variety_by_value(current_variety).flower_blooming_duration)).strftime('%Y-%m-%d')
                warning_blooming_date_prompt = create_harvest_question_response(
                    language=self.language,
                    conversation_state="WARNING_BLOOMING_DATE",
                    extra_context={
                        'variety_name': DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language),
                        'flower_blooming_duration': current_variety_flower_blooming_duration,
                        'initial_date': initial_date
                    }
                )
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=[blooming_date_info_prompt, warning_blooming_date_prompt]
                )
                return

            # Trigger state machine transition
            self.state_machine.blooming_date_selected()

            # Create prompt asking for grade and weight
            current_available_grades = DURIAN_INFO.get_grade_label_by_variety_value(current_variety, self.language) or []
            # Save current available grades to cache
            MemoryCache.set(self.line_user_id, 'current_available_grades', current_available_grades)

            if current_variety == "other":
                variety_name = self.state_machine.data['variety_details']['other']['variety_name']
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(current_variety, self.language)

            prompt_ask_grade_variety = create_harvest_question_response(
                self.language,
                'AWAITING_GRADE_WEIGHT_PER_VARIETY',
                extra_context={
                    'current_variety': variety_name,
                    'grades': current_available_grades
                }
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=[blooming_date_info_prompt, prompt_ask_grade_variety]
            )

        self.state_machine.save_state()

    def _process_cutter_selection_postback(self, data: str, reply_token: str):
        """Process cutter selection postback"""
        from urllib.parse import parse_qsl

        # Parse postback data
        postback_data = dict(parse_qsl(data))

        if postback_data.get('action') == 'select_cutter':
            cutter_id = postback_data.get('cutter_id')
            self.state_machine.data['cutter_id'] = cutter_id

            # Get cutter info from PTP controller
            cutter = get_farmer_cutter_info(cutter_id).get('data', {})
            if cutter:
                self.state_machine.data['cutter_name'] = cutter.get('first_name', '') + ' ' + cutter.get('last_name', '')
                self.state_machine.data['cutter_registration'] = cutter.get('profile', {}).get('metadata', {}).get('is_certified') or False
                self.state_machine.data['cutter_registration_number'] = cutter.get('profile', {}).get('metadata', {}).get('license_number', '')
                self.state_machine.data['cutter_profile_id'] = cutter.get('profile', {}).get('id', '')

                # Get cutter avatar info
                avatar = cutter.get('avatar', {})
                if avatar:
                    from utils.config import FILE_LINK_PREFIX
                    self.state_machine.data['cutter_image_id'] = avatar.get('id', '')
                    self.state_machine.data['cutter_image_link'] = f"{FILE_LINK_PREFIX}/{avatar.get('filename_disk', '')}"

            # Transition to cutter photo
            self.state_machine.cutter_selected()

            cutter_photo_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_CUTTER_PHOTO'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=cutter_photo_prompt
            )

        elif postback_data.get('action') == 'add_new_cutter':
            # Transition to cutter name input
            self.state_machine.add_new_cutter_selected()

            cutter_name_prompt = create_harvest_question_response(
                self.language,
                'AWAITING_CUTTER_NAME'
            )
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message=cutter_name_prompt
            )

        self.state_machine.save_state()

    def send_durian_info(self, reply_token: str = None):
        """Send durian information flex message to user"""
        from copy import copy
        from utils.flex_message import create_durian_info_flex_message
        from service.line_service import send_flex_message

        data = self.state_machine.data
        harvest_lot_name = data.get('harvest_lot_name', ' ')
        farm_name = data.get('farm_name', ' ')
        farm_address = data.get('farm_address', ' ')
        batchlot = data.get('batchlot', ' ')
        plots = data.get('selected_plots', [])
        varieties = copy(data.get('selected_varieties', []))
        raw_variety_details: dict = data.get('variety_details', {})
        variety_details = {}

        for index, variety in enumerate(varieties):
            if variety == 'other':
                variety_name = raw_variety_details['other']['variety_name']
            else:
                variety_name = DURIAN_INFO.mapping_variety_value_to_text(variety, self.language)
            grades_weights = {}
            for grade_value, weight in raw_variety_details[variety]['grades_weights'].items():
                grade_name = DURIAN_INFO.mapping_grade_value_to_text(variety, grade_value, self.language)
                grades_weights[grade_name] = weight
            variety_details[variety_name] = {
                'grades_weights': grades_weights,
                'blooming_date': raw_variety_details[variety]['blooming_date']
            }

        total_weight = data.get('total_weight', 0.0)
        image_links = data.get('image_links', [])
        harvest_date = data.get('harvest_date', ' ')
        packing_house = data.get('packing_house', ' ')
        cutter_name = data.get('cutter_name', ' ')
        cutter_avatar_link = data.get('cutter_image_link', ' ')
        cutter_registration = data.get('cutter_registration', False)
        doa_number = data.get('cutter_registration_number', None)
        vehicle_image_link = data.get('vehicle_image_link', ' ')
        vehicle_registration_number = data.get('vehicle_registration_number', ' ')
        vehicle_registration_province = data.get('vehicle_registration_province', ' ')

        durian_info_flex_msg = create_durian_info_flex_message(
            language=self.language,
            harvest_lot_name=harvest_lot_name,
            batchlot=batchlot,
            farm_name=farm_name,
            farm_address=farm_address,
            plots=plots,
            variety_details=variety_details,
            total_weight=total_weight,
            image_links=image_links,
            harvest_date=harvest_date,
            packing_house=packing_house,
            cutter_name=cutter_name,
            cutter_avatar_link=cutter_avatar_link,
            cutter_registration=cutter_registration,
            doa_number=doa_number,
            vehicle_image_link=vehicle_image_link,
            vehicle_registration_number=vehicle_registration_number,
            vehicle_registration_province=vehicle_registration_province
        )

        if reply_token:
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "flex", "altText": "Durian Information", "contents": durian_info_flex_msg}
            )
        else:
            send_flex_message(self.line_user_id, durian_info_flex_msg)

    def send_cutter_info(self, reply_token: str = None):
        """Send cutter information flex message to user"""
        from utils.flex_message import create_cutter_info_flex_message
        from service.line_service import send_flex_message

        data = self.state_machine.data
        cutter_info_flex_msg = create_cutter_info_flex_message(
            language=self.language,
            cutter_name=data['cutter_name'],
            avatar_link=data['cutter_image_link'],
            cutter_registration=data.get('cutter_registration', False),
            doa_number=data.get('cutter_registration_number', None),
            vehicle_image_link=data['vehicle_image_link'],
            vehicle_registration_number=data['vehicle_registration_number'],
            vehicle_registration_province=data['vehicle_registration_province']
        )

        if reply_token:
            send_line_message(
                line_user_id=self.line_user_id,
                reply_token=reply_token,
                message={"type": "flex", "altText": "Cutter Information", "contents": cutter_info_flex_msg}
            )
        else:
            send_flex_message(self.line_user_id, cutter_info_flex_msg)

    def _process_plot_certificate_image(self, image_content: bytes, reply_token: str):
        """Process plot certificate image"""
        from service.upload_file import send_bytes_form_data
        from utils.config import FILE_LINK_PREFIX

        # Upload image to remote storage
        response_data = send_bytes_form_data(image_content)
        remote_image_id = response_data['data']['id']
        image_link = f"{FILE_LINK_PREFIX}/{response_data['data']['filename_disk']}"
        logger.info(f"Uploaded plot certificate image to remote storage, image ID: {remote_image_id}")

        # Save plot certificate image info
        if 'selected_plots' not in self.state_machine.data:
            self.state_machine.data['selected_plots'] = []

        # Add plot certificate info to the last selected plot
        if self.state_machine.data['selected_plots']:
            self.state_machine.data['selected_plots'][-1]['plot_certificate_link'] = image_link
            self.state_machine.data['selected_plots'][-1]['plot_certificate_image_id'] = remote_image_id
        else:
            # If no selected plots, create one
            self.state_machine.data['selected_plots'] = [{
                'plot_certificate_link': image_link,
                'plot_certificate_image_id': remote_image_id
            }]

        # Trigger state machine transition
        self.state_machine.plot_certificate_uploaded()

        # Move to ask adding additional plots (the state machine will handle the next step)
        more_plots_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_ADDITIONAL_PLOTS'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=more_plots_prompt
        )

        self.state_machine.save_state()

    def _process_after_uploading_durian_photo(self, reply_token: str):
        """Process logic after durian photo is uploaded"""
        try:
            # Ensure durian_image_ids exists
            if 'durian_image_ids' not in self.state_machine.data:
                self.state_machine.data['durian_image_ids'] = []

            # Get user profile and check role
            ptp_user_id = self.database.get_line_user(query={'line_user_id': self.line_user_id}).user_id
            user_info = get_farmer_cutter_info(ptp_user_id)
            user_role = user_info.get('data', {}).get('profile', {}).get('role', '')

            # Store user role in state machine
            self.state_machine.user_role = user_role

            if user_role == 'cutter':
                # For cutters, get their info from profile
                self.state_machine.data['cutter_id'] = user_info['data']['id']
                self.state_machine.data['cutter_name'] = user_info['data']['first_name'] + ' ' + user_info['data']['last_name']
                self.state_machine.data['cutter_registration_number'] = user_info['data']['profile'].get('metadata', {}).get('license_number', '')
                self.state_machine.data['cutter_image_id'] = user_info.get('data', {}).get('avatar', {}).get('id', '')

                from utils.config import FILE_LINK_PREFIX
                self.state_machine.data['cutter_image_link'] = f"{FILE_LINK_PREFIX}/{user_info.get('data', {}).get('avatar', {}).get('filename_disk', '')}"
                self.state_machine.data['cutter_profile_id'] = user_info.get('data', {}).get('profile', {}).get('id', '')

                # Verify registration
                self.state_machine.data['cutter_registration'] = user_info.get('data', {}).get('profile', {}).get('metadata', {}).get('is_certified') or False

                # Move to vehicle photo
                self.state_machine.durian_photo_uploaded()

                vehicle_photo_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_VEHICLE_PHOTO'
                )

                # Send vehicle photo prompt
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=vehicle_photo_prompt
                )

            else:  # farmer
                # For farmers, ask for cutter selection
                self.state_machine.durian_photo_uploaded()

                cutters = get_list_cutters(ptp_user_id)
                cutter_selection_prompt = create_harvest_question_response(
                    self.language,
                    'AWAITING_CUTTER_SELECTION',
                    extra_context={'cutters': cutters}
                )
                send_line_message(
                    line_user_id=self.line_user_id,
                    reply_token=reply_token,
                    message=cutter_selection_prompt
                )

            self.state_machine.save_state()

        except Exception as e:
            logger.error(f"Error processing after durian photo upload: {str(e)}")
            if hasattr(e, 'response'):
                logger.error(f"API Response: {e.response.text}")
            raise

    def _process_durian_photo(self, image_content: bytes, reply_token: str):
        """Process durian photo"""
        from service.upload_file import send_bytes_form_data
        from utils.config import FILE_LINK_PREFIX

        # Upload image to remote storage
        response_data = send_bytes_form_data(image_content)
        remote_image_id = response_data['data']['id']
        image_link = f"{FILE_LINK_PREFIX}/{response_data['data']['filename_disk']}"
        logger.info(f"Uploaded durian photo to remote storage, image ID: {remote_image_id}")

        # Save image link and id
        if 'durian_image_ids' not in self.state_machine.data:
            self.state_machine.data['durian_image_ids'] = []
        if 'image_links' not in self.state_machine.data:
            self.state_machine.data['image_links'] = []

        self.state_machine.data['durian_image_ids'].append(remote_image_id)
        self.state_machine.data['image_links'].append(image_link)

        # Process after uploading durian photo using internal method
        self._process_after_uploading_durian_photo(reply_token)

    def _process_cutter_photo(self, image_content: bytes, reply_token: str):
        """Process cutter photo"""
        from service.upload_file import send_bytes_form_data
        from utils.config import FILE_LINK_PREFIX

        # Upload image to remote storage
        response_data = send_bytes_form_data(image_content)
        remote_image_id = response_data['data']['id']
        image_link = f"{FILE_LINK_PREFIX}/{response_data['data']['filename_disk']}"
        logger.info(f"Uploaded cutter photo to remote storage, image ID: {remote_image_id}")

        # Save cutter image info
        self.state_machine.data['cutter_image_id'] = remote_image_id
        self.state_machine.data['cutter_image_link'] = image_link

        # Trigger state machine transition
        self.state_machine.cutter_photo_uploaded()

        # Move to ask vehicle photo
        vehicle_photo_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_VEHICLE_PHOTO'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=vehicle_photo_prompt
        )

        self.state_machine.save_state()

    def _process_vehicle_photo(self, image_content: bytes, reply_token: str):
        """Process vehicle photo"""
        from service.upload_file import send_bytes_form_data
        from utils.config import FILE_LINK_PREFIX

        # Upload image to remote storage
        response_data = send_bytes_form_data(image_content)
        remote_image_id = response_data['data']['id']
        image_link = f"{FILE_LINK_PREFIX}/{response_data['data']['filename_disk']}"
        logger.info(f"Uploaded vehicle photo to remote storage, image ID: {remote_image_id}")

        # Save vehicle image info
        self.state_machine.data['vehicle_image_id'] = remote_image_id
        self.state_machine.data['vehicle_image_link'] = image_link

        # TODO: Use Computer Vision service to get vehicle registration details
        # self.state_machine.data['vehicle_registration_number'] = "77D1-80105"
        # self.state_machine.data['vehicle_registration_province'] = "BinhDinh"

        # Trigger state machine transition
        self.state_machine.vehicle_photo_uploaded()

        # Send cutter info summary before asking for vehicle registration
        self.send_cutter_info()

        # Move to ask vehicle registration number
        vehicle_registration_number_prompt = create_harvest_question_response(
            self.language,
            'AWAITING_VEHICLE_REGISTRATION_NUMBER'
        )
        send_line_message(
            line_user_id=self.line_user_id,
            reply_token=reply_token,
            message=vehicle_registration_number_prompt
        )

        self.state_machine.save_state()