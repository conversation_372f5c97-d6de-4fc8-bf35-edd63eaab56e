"""
Harvest State Machine for managing the durian harvesting flow.
Uses python-statemachine to handle state transitions and validations.
"""

from statemachine import StateMachine, State
from typing import Dict, Any, Optional
import json
from utils.logger import logger
from utils.memory_cache import MemoryCache
from utils.line_utils import create_harvest_question_response
from service.line_service import send_line_message
from utils.durian_info import DURIAN_INFO
from service.ptp_controller_service import get_list_farms, get_list_plots, get_list_cutters, get_list_packing_houses
from handler.webhook_handler_component.helper import send_cutter_info
from service.database_mongo_service import DATABASE_SERVICE_MONGO


class HarvestStateMachine(StateMachine):
    """State machine for managing harvest flow"""
    
    # Define all states
    init = State(initial=True)
    awaiting_harvest_lot_name = State()
    awaiting_farm_selection = State()
    awaiting_plot_selection = State()
    awaiting_plot_certificate_image = State()
    awaiting_additional_plots = State()
    awaiting_variety_selection = State()
    awaiting_add_variety_name = State()
    awaiting_additional_varieties = State()
    awaiting_blooming_date_per_variety = State()
    awaiting_grade_weight_per_variety = State()
    awaiting_weight_per_variety = State()
    awaiting_additional_grades = State()
    awaiting_grade_weight_confirmation = State()
    awaiting_blooming_date = State()
    awaiting_harvest_date = State()
    awaiting_durian_photo = State()
    awaiting_cutter_selection = State()
    awaiting_cutter_photo = State()
    awaiting_cutter_name = State()
    awaiting_cutter_registration = State()
    awaiting_cutter_registration_number = State()
    awaiting_vehicle_photo = State()
    awaiting_vehicle_registration_number = State()
    awaiting_vehicle_registration_province = State()
    awaiting_send_or_draft = State()
    awaiting_packing_house = State()
    awaiting_final_confirmation = State()
    awaiting_add_farm_name = State()
    awaiting_add_farm_location = State()
    awaiting_add_plot_name = State()
    awaiting_add_plot_id = State()
    awaiting_add_plot_gap = State()
    awaiting_add_plot_area = State()
    awaiting_add_plot_confirmation = State()
    completed = State(final=True)
    
    # Define transitions
    collect_harvest_info = (
        init.to(awaiting_harvest_lot_name)
    )
    
    harvest_lot_name_provided = (
        awaiting_harvest_lot_name.to(awaiting_farm_selection, cond="is_cutter") |
        awaiting_harvest_lot_name.to(awaiting_plot_selection, cond="is_farmer")
    )
    
    farm_selected = awaiting_farm_selection.to(awaiting_plot_selection)
    plot_selected = awaiting_plot_selection.to(awaiting_plot_certificate_image)
    plot_certificate_uploaded = awaiting_plot_certificate_image.to(awaiting_additional_plots)
    
    additional_plots_yes = awaiting_additional_plots.to(awaiting_plot_selection)
    additional_plots_no = awaiting_additional_plots.to(awaiting_harvest_date)
    
    harvest_date_selected = awaiting_harvest_date.to(awaiting_variety_selection)
    variety_selected = awaiting_variety_selection.to(awaiting_grade_weight_per_variety)
    
    grade_weight_provided = awaiting_grade_weight_per_variety.to(awaiting_weight_per_variety)
    weight_provided = (
        awaiting_weight_per_variety.to(awaiting_additional_grades, cond="has_more_grades") |
        awaiting_weight_per_variety.to(awaiting_grade_weight_confirmation, cond="no_more_grades")
    )
    
    additional_grades_yes = awaiting_additional_grades.to(awaiting_grade_weight_per_variety)
    additional_grades_no = awaiting_additional_grades.to(awaiting_grade_weight_confirmation)
    
    grade_weight_confirmed = (
        awaiting_grade_weight_confirmation.to(awaiting_additional_varieties, cond="has_more_varieties") |
        awaiting_grade_weight_confirmation.to(awaiting_durian_photo, cond="no_more_varieties")
    )
    
    additional_varieties_yes = awaiting_additional_varieties.to(awaiting_variety_selection)
    additional_varieties_no = awaiting_additional_varieties.to(awaiting_durian_photo)
    
    durian_photo_uploaded = (
        awaiting_durian_photo.to(awaiting_cutter_selection, cond="is_farmer") |
        awaiting_durian_photo.to(awaiting_vehicle_photo, cond="is_cutter")
    )
    
    cutter_selected = awaiting_cutter_selection.to(awaiting_vehicle_photo)
    vehicle_photo_uploaded = awaiting_vehicle_photo.to(awaiting_vehicle_registration_number)
    vehicle_registration_provided = awaiting_vehicle_registration_number.to(awaiting_vehicle_registration_province)
    vehicle_province_provided = awaiting_vehicle_registration_province.to(awaiting_send_or_draft)
    
    send_selected = awaiting_send_or_draft.to(awaiting_packing_house)
    draft_selected = awaiting_send_or_draft.to(completed)
    
    packing_house_selected = awaiting_packing_house.to(awaiting_final_confirmation)
    final_confirmed = awaiting_final_confirmation.to(completed)
    
    def __init__(self, line_user_id: str, language: str = 'thailand'):
        super().__init__()
        self.line_user_id = line_user_id
        self.language = language
        self.data = {}
        self.user_role = None
        self.database = DATABASE_SERVICE_MONGO()
        
    def is_cutter(self):
        """Check if user is a cutter"""
        return self.user_role == 'cutter'
        
    def is_farmer(self):
        """Check if user is a farmer"""
        return self.user_role == 'farmer'
        
    def has_more_grades(self):
        """Check if there are more grades to add for current variety"""
        current_variety = self.data.get('current_variety')
        if not current_variety:
            return False
        selected_grades = self.data.get('variety_details', {}).get(current_variety, {}).get('grades_weights', {}).keys()
        current_available_grades = MemoryCache.get(self.line_user_id, 'current_available_grades') or []
        return len(selected_grades) < len(current_available_grades)
        
    def no_more_grades(self):
        """Check if no more grades to add for current variety"""
        return not self.has_more_grades()
        
    def has_more_varieties(self):
        """Check if there are more varieties to add"""
        selected_varieties = self.data.get('selected_varieties', [])
        return len(selected_varieties) < len(DURIAN_INFO.get_varieties())
        
    def no_more_varieties(self):
        """Check if no more varieties to add"""
        return not self.has_more_varieties()
        
    def save_state(self):
        """Save current state and data to Redis"""
        MemoryCache.set(self.line_user_id, 'state', self.current_state.id.upper())
        MemoryCache.set(self.line_user_id, 'data', self.data)
        MemoryCache.set(self.line_user_id, 'user_role', self.user_role)
        
    def load_state(self):
        """Load state and data from Redis"""
        saved_state = MemoryCache.get(self.line_user_id, 'state')
        saved_data = MemoryCache.get(self.line_user_id, 'data') or {}
        saved_user_role = MemoryCache.get(self.line_user_id, 'user_role')
        
        self.data = saved_data
        self.user_role = saved_user_role
        
        if saved_state:
            # Convert state name to state object
            state_mapping = {
                'INIT': self.init,
                'AWAITING_HARVEST_LOT_NAME': self.awaiting_harvest_lot_name,
                'AWAITING_FARM_SELECTION': self.awaiting_farm_selection,
                'AWAITING_PLOT_SELECTION': self.awaiting_plot_selection,
                'AWAITING_PLOT_CERTIFICATE_IMAGE': self.awaiting_plot_certificate_image,
                'AWAITING_ADDITIONAL_PLOTS': self.awaiting_additional_plots,
                'AWAITING_VARIETY_SELECTION': self.awaiting_variety_selection,
                'AWAITING_ADD_VARIETY_NAME': self.awaiting_add_variety_name,
                'AWAITING_ADDITIONAL_VARIETIES': self.awaiting_additional_varieties,
                'AWAITING_BLOOMING_DATE_PER_VARIETY': self.awaiting_blooming_date_per_variety,
                'AWAITING_GRADE_WEIGHT_PER_VARIETY': self.awaiting_grade_weight_per_variety,
                'AWAITING_WEIGHT_PER_VARIETY': self.awaiting_weight_per_variety,
                'AWAITING_ADDITIONAL_GRADES': self.awaiting_additional_grades,
                'AWAITING_GRADE_WEIGHT_CONFIRMATION': self.awaiting_grade_weight_confirmation,
                'AWAITING_BLOOMING_DATE': self.awaiting_blooming_date,
                'AWAITING_HARVEST_DATE': self.awaiting_harvest_date,
                'AWAITING_DURIAN_PHOTO': self.awaiting_durian_photo,
                'AWAITING_CUTTER_SELECTION': self.awaiting_cutter_selection,
                'AWAITING_CUTTER_PHOTO': self.awaiting_cutter_photo,
                'AWAITING_CUTTER_NAME': self.awaiting_cutter_name,
                'AWAITING_CUTTER_REGISTRATION': self.awaiting_cutter_registration,
                'AWAITING_CUTTER_REGISTRATION_NUMBER': self.awaiting_cutter_registration_number,
                'AWAITING_VEHICLE_PHOTO': self.awaiting_vehicle_photo,
                'AWAITING_VEHICLE_REGISTRATION_NUMBER': self.awaiting_vehicle_registration_number,
                'AWAITING_VEHICLE_REGISTRATION_PROVINCE': self.awaiting_vehicle_registration_province,
                'AWAITING_SEND_OR_DRAFT': self.awaiting_send_or_draft,
                'AWAITING_PACKING_HOUSE': self.awaiting_packing_house,
                'AWAITING_FINAL_CONFIRMATION': self.awaiting_final_confirmation,
                'AWAITING_ADD_FARM_NAME': self.awaiting_add_farm_name,
                'AWAITING_ADD_FARM_LOCATION': self.awaiting_add_farm_location,
                'AWAITING_ADD_PLOT_NAME': self.awaiting_add_plot_name,
                'AWAITING_ADD_PLOT_ID': self.awaiting_add_plot_id,
                'AWAITING_ADD_PLOT_GAP': self.awaiting_add_plot_gap,
                'AWAITING_ADD_PLOT_AREA': self.awaiting_add_plot_area,
                'AWAITING_ADD_PLOT_CONFIRMATION': self.awaiting_add_plot_confirmation,
                'COMPLETED': self.completed
            }
            
            if saved_state in state_mapping:
                self._current_state = state_mapping[saved_state]
                
    def get_current_state_name(self):
        """Get current state name in uppercase format"""
        return self.current_state.id.upper()
