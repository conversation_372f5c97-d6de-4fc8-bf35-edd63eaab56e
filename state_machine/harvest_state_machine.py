"""
Harvest State Machine for managing the durian harvesting flow.
Uses python-statemachine to handle state transitions and validations.
"""

from statemachine import StateMachine, State
from utils.logger import logger
from utils.memory_cache import MemoryCache
from utils.durian_info import DURIAN_INFO
from service.database_mongo_service import DATABASE_SERVICE_MONGO


class HarvestStateMachine(StateMachine):
    """State machine for managing harvest flow"""

    # Define all states
    init = State(initial=True)
    awaiting_harvest_lot_name = State()
    awaiting_farm_selection = State()
    awaiting_plot_selection = State()
    awaiting_plot_certificate_image = State()
    awaiting_additional_plots = State()
    awaiting_variety_selection = State()
    awaiting_add_variety_name = State()
    awaiting_additional_varieties = State()
    awaiting_blooming_date_per_variety = State()
    awaiting_grade_weight_per_variety = State()
    awaiting_weight_per_variety = State()
    awaiting_additional_grades = State()
    awaiting_grade_weight_confirmation = State()
    awaiting_blooming_date = State()
    awaiting_harvest_date = State()
    awaiting_durian_photo = State()
    awaiting_cutter_selection = State()
    awaiting_cutter_photo = State()
    awaiting_cutter_name = State()
    awaiting_cutter_registration = State()
    awaiting_cutter_registration_number = State()
    awaiting_vehicle_photo = State()
    awaiting_vehicle_registration_number = State()
    awaiting_vehicle_registration_province = State()
    awaiting_send_or_draft = State()
    awaiting_packing_house = State()
    awaiting_final_confirmation = State()
    awaiting_add_farm_name = State()
    awaiting_add_farm_location = State()
    awaiting_add_plot_name = State()
    awaiting_add_plot_id = State()
    awaiting_add_plot_gap = State()
    awaiting_add_plot_area = State()
    awaiting_add_plot_confirmation = State()
    completed = State(final=True)

    # Define transitions
    collect_harvest_info = (
        init.to(awaiting_harvest_lot_name)
    )

    harvest_lot_name_provided = (
        awaiting_harvest_lot_name.to(awaiting_farm_selection, cond="is_cutter",
                                     before="before_harvest_lot_name_provided",
                                     after="after_harvest_lot_name_provided") |
        awaiting_harvest_lot_name.to(awaiting_plot_selection, cond="is_farmer",
                                     before="before_harvest_lot_name_provided",
                                     after="after_harvest_lot_name_provided")
    )

    farm_selected = awaiting_farm_selection.to(awaiting_plot_selection,
                                               before="before_farm_selected",
                                               after="after_farm_selected")
    plot_selected = awaiting_plot_selection.to(awaiting_plot_certificate_image,
                                               before="before_plot_selected",
                                               after="after_plot_selected")
    plot_certificate_uploaded = awaiting_plot_certificate_image.to(awaiting_additional_plots)

    additional_plots_yes = awaiting_additional_plots.to(awaiting_plot_selection)
    additional_plots_no = awaiting_additional_plots.to(awaiting_harvest_date)

    harvest_date_selected = awaiting_harvest_date.to(awaiting_variety_selection)
    variety_selected = awaiting_variety_selection.to(awaiting_grade_weight_per_variety)

    grade_weight_provided = awaiting_grade_weight_per_variety.to(awaiting_weight_per_variety)
    weight_provided = (
        awaiting_weight_per_variety.to(awaiting_additional_grades, cond="has_more_grades") |
        awaiting_weight_per_variety.to(awaiting_grade_weight_confirmation, cond="no_more_grades")
    )

    additional_grades_yes = awaiting_additional_grades.to(awaiting_grade_weight_per_variety)
    additional_grades_no = awaiting_additional_grades.to(awaiting_grade_weight_confirmation)

    grade_weight_confirmed = (
        awaiting_grade_weight_confirmation.to(awaiting_additional_varieties, cond="has_more_varieties",
                                              before="before_grade_weight_confirmed",
                                              after="after_grade_weight_confirmed") |
        awaiting_grade_weight_confirmation.to(awaiting_durian_photo, cond="no_more_varieties",
                                              before="before_grade_weight_confirmed",
                                              after="after_grade_weight_confirmed")
    )

    additional_varieties_yes = awaiting_additional_varieties.to(awaiting_variety_selection)
    additional_varieties_no = awaiting_additional_varieties.to(awaiting_durian_photo)

    durian_photo_uploaded = (
        awaiting_durian_photo.to(awaiting_cutter_selection, cond="is_farmer") |
        awaiting_durian_photo.to(awaiting_vehicle_photo, cond="is_cutter")
    )

    cutter_selected = awaiting_cutter_selection.to(awaiting_cutter_photo)
    cutter_photo_uploaded = awaiting_cutter_photo.to(awaiting_vehicle_photo)
    vehicle_photo_uploaded = awaiting_vehicle_photo.to(awaiting_vehicle_registration_number)
    vehicle_registration_provided = awaiting_vehicle_registration_number.to(awaiting_vehicle_registration_province)
    vehicle_province_provided = awaiting_vehicle_registration_province.to(awaiting_send_or_draft)

    send_selected = awaiting_send_or_draft.to(awaiting_packing_house,
                                             before="before_send_selected",
                                             after="after_send_selected")
    draft_selected = awaiting_send_or_draft.to(completed)

    packing_house_selected = awaiting_packing_house.to(awaiting_final_confirmation,
                                                       before="before_packing_house_selected",
                                                       after="after_packing_house_selected")

    # Additional transitions for new methods
    variety_name_provided = awaiting_add_variety_name.to(awaiting_blooming_date_per_variety)
    cutter_name_provided = awaiting_cutter_name.to(awaiting_cutter_registration)
    cutter_registration_provided = awaiting_cutter_registration.to(awaiting_cutter_registration_number, cond="is_registered") | awaiting_cutter_registration.to(awaiting_cutter_photo, cond="not_registered")
    cutter_registration_number_provided = awaiting_cutter_registration_number.to(awaiting_cutter_photo)
    add_new_cutter_selected = awaiting_cutter_selection.to(awaiting_cutter_name)
    final_confirmation_confirmed = awaiting_final_confirmation.to(completed)
    blooming_date_selected = awaiting_blooming_date_per_variety.to(awaiting_grade_weight_per_variety)

    def __init__(self, line_user_id: str, language: str = 'thailand'):
        super().__init__()
        self.line_user_id = line_user_id
        self.language = language
        self.data = {}
        self.user_role = None
        self.database = DATABASE_SERVICE_MONGO()

    def is_cutter(self):
        """Check if user is a cutter"""
        return self.user_role == 'cutter'

    def is_farmer(self):
        """Check if user is a farmer"""
        return self.user_role == 'farmer'

    def has_more_grades(self):
        """Check if there are more grades to add for current variety"""
        current_variety = self.data.get('current_variety')
        if not current_variety:
            return False
        selected_grades = self.data.get('variety_details', {}).get(current_variety, {}).get('grades_weights', {}).keys()
        current_available_grades = MemoryCache.get(self.line_user_id, 'current_available_grades') or []
        return len(selected_grades) < len(current_available_grades)

    def no_more_grades(self):
        """Check if no more grades to add for current variety"""
        return not self.has_more_grades()

    def has_more_varieties(self):
        """Check if there are more varieties to add"""
        selected_varieties = self.data.get('selected_varieties', [])
        return len(selected_varieties) < len(DURIAN_INFO.get_varieties())

    def no_more_varieties(self):
        """Check if no more varieties to add"""
        return not self.has_more_varieties()

    def save_state(self):
        """Save current state and data to Redis"""
        MemoryCache.set(self.line_user_id, 'state', self.current_state.id.upper())
        MemoryCache.set(self.line_user_id, 'data', self.data)
        MemoryCache.set(self.line_user_id, 'user_role', self.user_role)

    def load_state(self):
        """Load state and data from Redis"""
        saved_state = MemoryCache.get(self.line_user_id, 'state')
        saved_data = MemoryCache.get(self.line_user_id, 'data') or {}
        saved_user_role = MemoryCache.get(self.line_user_id, 'user_role')

        self.data = saved_data
        self.user_role = saved_user_role

        if saved_state:
            # Convert state name to state object
            state_mapping = {
                'INIT': self.init,
                'AWAITING_HARVEST_LOT_NAME': self.awaiting_harvest_lot_name,
                'AWAITING_FARM_SELECTION': self.awaiting_farm_selection,
                'AWAITING_PLOT_SELECTION': self.awaiting_plot_selection,
                'AWAITING_PLOT_CERTIFICATE_IMAGE': self.awaiting_plot_certificate_image,
                'AWAITING_ADDITIONAL_PLOTS': self.awaiting_additional_plots,
                'AWAITING_VARIETY_SELECTION': self.awaiting_variety_selection,
                'AWAITING_ADD_VARIETY_NAME': self.awaiting_add_variety_name,
                'AWAITING_ADDITIONAL_VARIETIES': self.awaiting_additional_varieties,
                'AWAITING_BLOOMING_DATE_PER_VARIETY': self.awaiting_blooming_date_per_variety,
                'AWAITING_GRADE_WEIGHT_PER_VARIETY': self.awaiting_grade_weight_per_variety,
                'AWAITING_WEIGHT_PER_VARIETY': self.awaiting_weight_per_variety,
                'AWAITING_ADDITIONAL_GRADES': self.awaiting_additional_grades,
                'AWAITING_GRADE_WEIGHT_CONFIRMATION': self.awaiting_grade_weight_confirmation,
                'AWAITING_BLOOMING_DATE': self.awaiting_blooming_date,
                'AWAITING_HARVEST_DATE': self.awaiting_harvest_date,
                'AWAITING_DURIAN_PHOTO': self.awaiting_durian_photo,
                'AWAITING_CUTTER_SELECTION': self.awaiting_cutter_selection,
                'AWAITING_CUTTER_PHOTO': self.awaiting_cutter_photo,
                'AWAITING_CUTTER_NAME': self.awaiting_cutter_name,
                'AWAITING_CUTTER_REGISTRATION': self.awaiting_cutter_registration,
                'AWAITING_CUTTER_REGISTRATION_NUMBER': self.awaiting_cutter_registration_number,
                'AWAITING_VEHICLE_PHOTO': self.awaiting_vehicle_photo,
                'AWAITING_VEHICLE_REGISTRATION_NUMBER': self.awaiting_vehicle_registration_number,
                'AWAITING_VEHICLE_REGISTRATION_PROVINCE': self.awaiting_vehicle_registration_province,
                'AWAITING_SEND_OR_DRAFT': self.awaiting_send_or_draft,
                'AWAITING_PACKING_HOUSE': self.awaiting_packing_house,
                'AWAITING_FINAL_CONFIRMATION': self.awaiting_final_confirmation,
                'AWAITING_ADD_FARM_NAME': self.awaiting_add_farm_name,
                'AWAITING_ADD_FARM_LOCATION': self.awaiting_add_farm_location,
                'AWAITING_ADD_PLOT_NAME': self.awaiting_add_plot_name,
                'AWAITING_ADD_PLOT_ID': self.awaiting_add_plot_id,
                'AWAITING_ADD_PLOT_GAP': self.awaiting_add_plot_gap,
                'AWAITING_ADD_PLOT_AREA': self.awaiting_add_plot_area,
                'AWAITING_ADD_PLOT_CONFIRMATION': self.awaiting_add_plot_confirmation,
                'COMPLETED': self.completed
            }

            if saved_state in state_mapping:
                self._current_state = state_mapping[saved_state]

    def get_current_state_name(self):
        """Get current state name in uppercase format"""
        return self.current_state.id.upper()

    # ============ CALLBACK METHODS ============

    def before_harvest_lot_name_provided(self):
        """Called before transitioning from harvest lot name to next state"""
        logger.info(f"Before harvest_lot_name_provided: {self.data.get('harvest_lot_name')}")

    def after_harvest_lot_name_provided(self):
        """Called after transitioning from harvest lot name to next state"""
        logger.info(f"After harvest_lot_name_provided: Current state is {self.get_current_state_name()}")
        # Send appropriate question based on user role
        self._send_next_question_after_harvest_lot_name()

    def before_farm_selected(self):
        """Called before farm selection transition"""
        logger.info(f"Before farm_selected: {self.data.get('selected_farm')}")

    def after_farm_selected(self):
        """Called after farm selection transition"""
        logger.info(f"After farm_selected: Current state is {self.get_current_state_name()}")
        # Send plot selection question
        self._send_plot_selection_question()

    def before_plot_selected(self):
        """Called before plot selection transition"""
        logger.info(f"Before plot_selected: {self.data.get('selected_plot')}")

    def after_plot_selected(self):
        """Called after plot selection transition"""
        logger.info(f"After plot_selected: Current state is {self.get_current_state_name()}")
        # Send plot certificate image request
        self._send_plot_certificate_request()

    def before_grade_weight_confirmed(self):
        """Called before grade weight confirmation transition"""
        logger.info(f"Before grade_weight_confirmed: Current variety processing complete")

    def after_grade_weight_confirmed(self):
        """Called after grade weight confirmation transition"""
        logger.info(f"After grade_weight_confirmed: Current state is {self.get_current_state_name()}")
        # Send next question based on whether more varieties are available
        self._send_next_question_after_grade_weight_confirmation()

    def before_send_selected(self):
        """Called before send to packing house transition"""
        logger.info("Before send_selected: Preparing to send to packing house")

    def after_send_selected(self):
        """Called after send to packing house transition"""
        logger.info(f"After send_selected: Current state is {self.get_current_state_name()}")
        # Send packing house selection question
        self._send_packing_house_selection()

    def before_packing_house_selected(self):
        """Called before packing house selection transition"""
        logger.info(f"Before packing_house_selected: Creating harvest event")
        # Create harvest event in PTP controller
        self._create_harvest_event()

    def after_packing_house_selected(self):
        """Called after packing house selection transition"""
        logger.info(f"After packing_house_selected: Current state is {self.get_current_state_name()}")
        # Send final confirmation
        self._send_final_confirmation()

    # ============ HELPER METHODS FOR CALLBACKS ============

    def _send_next_question_after_harvest_lot_name(self):
        """Send appropriate question after harvest lot name is provided"""
        # This will be implemented based on user role
        pass

    def _send_plot_selection_question(self):
        """Send plot selection question"""
        # This will be implemented to send plot selection options
        pass

    def _send_plot_certificate_request(self):
        """Send plot certificate image request"""
        # This will be implemented to request plot certificate image
        pass

    def _send_next_question_after_grade_weight_confirmation(self):
        """Send next question after grade weight confirmation"""
        # This will be implemented based on available varieties
        pass

    def _send_packing_house_selection(self):
        """Send packing house selection question"""
        # This will be implemented to send packing house options
        pass

    def _create_harvest_event(self):
        """Create harvest event in PTP controller"""
        # This will be implemented to create the event
        pass

    def _send_final_confirmation(self):
        """Send final confirmation message"""
        # This will be implemented to send final confirmation
        pass

    def has_more_varieties(self):
        """Check if there are more varieties to process"""
        selected_varieties = self.data.get('selected_varieties', [])
        available_varieties = DURIAN_INFO.get_varieties()
        return len(selected_varieties) < len(available_varieties)

    def is_registered(self):
        """Check if cutter is registered"""
        return self.data.get('cutter_registration', False)

    def not_registered(self):
        """Check if cutter is not registered"""
        return not self.data.get('cutter_registration', False)
